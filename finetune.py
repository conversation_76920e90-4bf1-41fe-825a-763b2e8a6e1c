import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'
import torch.utils.data
from torch import nn
from torch.nn import DataParallel
from datetime import datetime
from config import BATCH_SIZE, SAVE_FREQ, SAVE_DIR, TEST_FREQ, GPU
from config import CASIA_DATA_DIR, LFW_DATA_DIR
from core import model
from core.utils import init_log
from dataloader.CASIA_Face_loader import CASIA_Face
from dataloader.CASIA_Face_subset_loader import CASIA_Face_Subset_Loader
import torch.optim as optim
import time
from torch.optim import lr_scheduler
import numpy as np
import argparse
from tqdm import tqdm



# python finetune.py --use-subset --save-model --lr 0.001

def parse_args():
    parser = argparse.ArgumentParser(description='MobileFaceNet微调')
    parser.add_argument('--pretrained', type=str, default='./model/best/068.ckpt',
                        help='预训练模型路径')
    parser.add_argument('--epochs', type=int, default=20,
                        help='微调的总epoch数')
    parser.add_argument('--lr', type=float, default=0.0001,
                        help='初始学习率')
    parser.add_argument('--model-prefix', type=str, default='MobileFaceNet_CBAM_',
                        help='模型保存前缀')
    parser.add_argument('--batch-size', type=int, default=64,
                        help='批量大小')
    # 添加用于CASIA子集的参数
    parser.add_argument('--use-subset', action='store_true',
                        help='是否使用CASIA子集进行训练')
    parser.add_argument('--subset-file', type=str, default='./output/casia_subset_images_500ids_20min_42.txt',
                        help='CASIA子集文件路径，仅当--use-subset为True时有效')
    # 新增参数：是否在训练期间保存模型
    parser.add_argument('--save-model', action='store_true',
                        help='是否在训练期间保存模型')
    # 新增参数：验证集比例
    parser.add_argument('--val-ratio', type=float, default=0.2,
                        help='验证集占总数据的比例')
    return parser.parse_args()

def main():
    args = parse_args()
    
    # gpu初始化
    gpu_list = ''
    multi_gpus = False
    if isinstance(GPU, int):
        gpu_list = str(GPU)
    else:
        multi_gpus = True
        for i, gpu_id in enumerate(GPU):
            gpu_list += str(gpu_id)
            if i != len(GPU) - 1:
                gpu_list += ','
    os.environ['CUDA_VISIBLE_DEVICES'] = gpu_list

    # 其他初始化
    start_epoch = 1
    save_dir = os.path.join(SAVE_DIR, args.model_prefix + datetime.now().strftime('%Y%m%d_%H%M%S'))
    if os.path.exists(save_dir):
        raise NameError('模型目录已存在!')
    os.makedirs(save_dir)
    logging = init_log(save_dir)
    _print = logging.info

    # 定义数据加载器 - 根据参数选择使用原始数据集或子集
    if args.use_subset and args.subset_file:
        _print(f"使用CASIA子集进行训练: {args.subset_file}")
        full_dataset = CASIA_Face_Subset_Loader(root=CASIA_DATA_DIR, subset_file=args.subset_file)
    else:
        _print("使用完整CASIA-WebFace数据集进行训练")
        full_dataset = CASIA_Face(root=CASIA_DATA_DIR)
    
    # 划分训练集和验证集
    _print(f"将数据集划分为训练集({(1-args.val_ratio)*100:.0f}%)和验证集({args.val_ratio*100:.0f}%)...")
    dataset_size = len(full_dataset)
    val_size = int(dataset_size * args.val_ratio)
    train_size = dataset_size - val_size
    
    # 设置随机种子以确保可重复性
    torch.manual_seed(42)
    trainset, valset = torch.utils.data.random_split(full_dataset, [train_size, val_size])
    
    _print(f"训练集样本数: {len(trainset)}")
    _print(f"验证集样本数: {len(valset)}")
    
    # 创建训练集和验证集的数据加载器
    trainloader = torch.utils.data.DataLoader(trainset, batch_size=args.batch_size,
                                             shuffle=True, num_workers=8, drop_last=False)
    valloader = torch.utils.data.DataLoader(valset, batch_size=args.batch_size,
                                           shuffle=False, num_workers=8, drop_last=False)

    # 定义模型
    net = model.MobileFacenet()
    ArcMargin = model.ArcMarginProduct(128, full_dataset.class_nums)

    # 加载预训练模型
    if os.path.exists(args.pretrained):
        _print(f'加载预训练模型: {args.pretrained}')
        ckpt = torch.load(args.pretrained)
        # 仅加载除CBAM模块以外的模型参数
        model_dict = net.state_dict()
        pretrained_dict = {k: v for k, v in ckpt['net_state_dict'].items() if 'cbam' not in k and k in model_dict}
        model_dict.update(pretrained_dict)
        net.load_state_dict(model_dict)
        _print('预训练模型加载成功，CBAM模块将随机初始化')
    else:
        _print(f'未找到预训练模型: {args.pretrained}, 从头开始训练')

    # 冻结初始层参数
    # 冻结3×3卷积
    for param in net.conv1.parameters():
        param.requires_grad = False
    _print('已冻结初始3×3卷积层参数')
    
    # 冻结3×3深度卷积
    for param in net.dw_conv1.parameters():
        param.requires_grad = False
    _print('已冻结3×3深度卷积层参数')
    
    # 冻结第一个瓶颈层（前5个blocks）
    for i in range(5):
        for param in net.blocks[i].parameters():
            param.requires_grad = False
    _print('已冻结第一个瓶颈层参数（前5个blocks）')
    
    # 冻结第二个瓶颈层（第6个block）
    for param in net.blocks[5].parameters():
        param.requires_grad = False
    _print('已冻结第二个瓶颈层参数（第6个block）')

    # 收集已冻结的参数ID
    frozen_param_ids = []
    for param in net.conv1.parameters():
        frozen_param_ids.append(id(param))
    for param in net.dw_conv1.parameters():
        frozen_param_ids.append(id(param))
    for i in range(6):  # 修改为6，包含第二个瓶颈层
        for param in net.blocks[i].parameters():
            frozen_param_ids.append(id(param))
    
    # 创建互斥的参数组 - 使用参数名称作为键以避免重复
    param_groups = {
        'cbam': [],       # CBAM模块参数
        'linear1': [],    # 线性层参数
        'arcmargin': [],  # ArcMargin参数
        'prelu': [],      # PReLU参数
        'base': []        # 其他基础参数
    }
    param_id_to_group = {}  # 记录每个参数ID属于哪个组
    
    # 首先收集CBAM模块参数
    for name, module in net.named_modules():
        if 'cbam' in name:
            for param_name, param in module.named_parameters():
                if param.requires_grad and id(param) not in frozen_param_ids:
                    param_groups['cbam'].append(param)
                    param_id_to_group[id(param)] = 'cbam'
    
    # 收集linear1参数
    for param in net.linear1.parameters():
        if param.requires_grad and id(param) not in frozen_param_ids:
            if id(param) not in param_id_to_group:
                param_groups['linear1'].append(param)
                param_id_to_group[id(param)] = 'linear1'
    
    # 收集ArcMargin参数
    if hasattr(ArcMargin, 'weight'):
        if ArcMargin.weight.requires_grad and id(ArcMargin.weight) not in frozen_param_ids:
            if id(ArcMargin.weight) not in param_id_to_group:
                param_groups['arcmargin'].append(ArcMargin.weight)
                param_id_to_group[id(ArcMargin.weight)] = 'arcmargin'
    
    # 收集PReLU参数
    for m in net.modules():
        if isinstance(m, nn.PReLU):
            for param in m.parameters():
                if param.requires_grad and id(param) not in frozen_param_ids:
                    if id(param) not in param_id_to_group:
                        param_groups['prelu'].append(param)
                        param_id_to_group[id(param)] = 'prelu'
    
    # 收集其他基础参数（不在其他组中且未冻结的）
    for name, param in net.named_parameters():
        if param.requires_grad and id(param) not in frozen_param_ids:
            if id(param) not in param_id_to_group:
                param_groups['base'].append(param)
                param_id_to_group[id(param)] = 'base'
    
    # 输出各参数组的大小，便于调试
    _print(f"CBAM模块参数数量: {len(param_groups['cbam'])}")
    _print(f"linear1参数数量: {len(param_groups['linear1'])}")
    _print(f"ArcMargin参数数量: {len(param_groups['arcmargin'])}")
    _print(f"PReLU参数数量: {len(param_groups['prelu'])}")
    _print(f"基础参数数量: {len(param_groups['base'])}")
    
    # 验证总参数数量
    total_grouped = sum(len(group) for group in param_groups.values())
    total_trainable = sum(1 for p in net.parameters() if p.requires_grad) + sum(1 for p in ArcMargin.parameters() if p.requires_grad)
    _print(f"参数分组总数: {total_grouped}, 可训练参数总数: {total_trainable}")
    
    # 为CBAM模块使用更高的学习率
    optimizer_ft = optim.SGD([
        {'params': param_groups['base'], 'weight_decay': 4e-5, 'lr': args.lr * 0.1},
        {'params': param_groups['linear1'], 'weight_decay': 4e-4, 'lr': args.lr * 0.1},
        {'params': param_groups['arcmargin'], 'weight_decay': 4e-4, 'lr': args.lr * 0.1},
        {'params': param_groups['prelu'], 'weight_decay': 0.0, 'lr': args.lr * 0.1},
        {'params': param_groups['cbam'], 'weight_decay': 4e-4, 'lr': args.lr}  # CBAM模块使用较高学习率
    ], lr=args.lr, momentum=0.9, nesterov=True)

    exp_lr_scheduler = lr_scheduler.MultiStepLR(optimizer_ft, milestones=[int(args.epochs*0.6), int(args.epochs*0.8)], gamma=0.1)

    net = net.cuda()
    ArcMargin = ArcMargin.cuda()
    if multi_gpus:
        net = DataParallel(net)
        ArcMargin = DataParallel(ArcMargin)
    criterion = torch.nn.CrossEntropyLoss()

    # 打印模型训练状态
    _print('模型参数状态:')
    total_params = sum(p.numel() for p in net.parameters())
    trainable_params_count = sum(p.numel() for p in net.parameters() if p.requires_grad)
    frozen_params_count = total_params - trainable_params_count
    _print(f'总参数数量: {total_params}')
    _print(f'可训练参数数量: {trainable_params_count} ({trainable_params_count/total_params*100:.2f}%)')
    _print(f'冻结参数数量: {frozen_params_count} ({frozen_params_count/total_params*100:.2f}%)')

    best_val_loss = float('inf')
    best_epoch = 0
    for epoch in range(start_epoch, args.epochs + 1):
        # 训练模型
        net.train()
        
        # 获取当前学习率
        current_lr = optimizer_ft.param_groups[0]['lr']
        _print(f'当前学习率: {current_lr}')

        train_total_loss = 0.0
        total = 0
        since = time.time()
        
        # 使用tqdm创建进度条
        pbar = tqdm(trainloader, desc=f'Epoch {epoch}/{args.epochs}')
        for data in pbar:
            img, label = data[0].cuda(), data[1].cuda()
            batch_size = img.size(0)
            optimizer_ft.zero_grad()

            raw_logits = net(img)
            output = ArcMargin(raw_logits, label)
            total_loss = criterion(output, label)
            total_loss.backward()
            optimizer_ft.step()

            # 更新损失和样本数
            train_total_loss += total_loss.item() * batch_size
            total += batch_size
            
            # 更新进度条显示的损失值
            pbar.set_postfix({'loss': total_loss.item()})
        
        # 计算平均训练损失
        train_total_loss = train_total_loss / total
        
        # 在验证集上评估模型
        net.eval()
        val_total_loss = 0.0
        val_total = 0
        val_start = time.time()
        
        _print('在验证集上评估...')
        with torch.no_grad():
            pbar_val = tqdm(valloader, desc=f'验证 Epoch {epoch}/{args.epochs}')
            for data in pbar_val:
                img, label = data[0].cuda(), data[1].cuda()
                batch_size = img.size(0)
                
                raw_logits = net(img)
                output = ArcMargin(raw_logits, label)
                val_loss = criterion(output, label)
                
                # 更新验证损失和样本数
                val_total_loss += val_loss.item() * batch_size
                val_total += batch_size
                
                # 更新进度条显示的损失值
                pbar_val.set_postfix({'val_loss': val_loss.item()})
        
        # 计算平均验证损失和时间
        val_total_loss = val_total_loss / val_total
        val_time = time.time() - val_start
        train_time = time.time() - since - (time.time() - val_start)
        
        # 在优化器步骤之后调用学习率调度器
        exp_lr_scheduler.step()
        
        # 输出本轮训练和验证的详细信息
        _print('Epoch {}/{} | 学习率: {:.7f} | 训练损失: {:.4f} | 验证损失: {:.4f} | 训练时长: {:.0f}分{:.0f}秒 | 验证时长: {:.0f}分{:.0f}秒'.format(
            epoch, args.epochs, current_lr, train_total_loss, val_total_loss,
            train_time // 60, train_time % 60, val_time // 60, val_time % 60))

        # 保存验证损失最小的模型作为最佳模型
        if val_total_loss < best_val_loss:
            best_val_loss = val_total_loss
            best_epoch = epoch
            _print('发现新的最佳模型! 验证损失: {:.4f}'.format(best_val_loss))
            if args.save_model:
                if multi_gpus:
                    net_state_dict = net.module.state_dict()
                else:
                    net_state_dict = net.state_dict()
                torch.save({
                    'epoch': epoch,
                    'net_state_dict': net_state_dict,
                    'val_loss': best_val_loss},
                    os.path.join(save_dir, 'best.ckpt'))

        # 保存模型
        if args.save_model and epoch % SAVE_FREQ == 0:
            _print('保存检查点: {}'.format(epoch))
            if multi_gpus:
                net_state_dict = net.module.state_dict()
            else:
                net_state_dict = net.state_dict()
            if not os.path.exists(save_dir):
                os.mkdir(save_dir)
            torch.save({
                'epoch': epoch,
                'net_state_dict': net_state_dict},
                os.path.join(save_dir, '%03d.ckpt' % epoch))
        
        _print('-'*80)  # 添加分隔线使日志更清晰
    
    # 训练结束后保存最终模型
    if args.save_model:
        _print('保存最终模型...')
        if multi_gpus:
            net_state_dict = net.module.state_dict()
        else:
            net_state_dict = net.state_dict()
        torch.save({
            'epoch': args.epochs,
            'net_state_dict': net_state_dict},
            os.path.join(save_dir, 'final.ckpt'))
    
    _print('训练完成! 最佳验证损失: {:.4f} (Epoch: {})'.format(best_val_loss, best_epoch))

if __name__ == '__main__':
    main() 