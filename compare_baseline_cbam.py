#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基准模型与CBAM模型比较脚本
专门用于比较MobileFaceNet基准模型和MobileFaceNet+CBAM模型在RFW数据集上的性能
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rfw_evaluation import run_model_comparison, find_baseline_model, find_cbam_model


def main():
    """主函数"""
    print("🔄 基准模型 vs CBAM模型性能比较")
    print("="*60)
    
    # 检查数据集是否存在
    rfw_data_dir = './data/RFW'
    if not os.path.exists(rfw_data_dir):
        print(f"❌ RFW数据集不存在: {rfw_data_dir}")
        print("请确保RFW数据集已正确放置在data/RFW目录下")
        return
    
    # 检查模型是否存在
    base_model = find_baseline_model()
    cbam_model = find_cbam_model()
    
    if not base_model:
        print("❌ 未找到基准模型")
        print("请确保存在基准MobileFaceNet模型文件")
        print("预期路径: model/mobilefacenet_best/068.ckpt")
        return
    
    if not cbam_model:
        print("❌ 未找到CBAM模型")
        print("请确保存在MobileFaceNet+CBAM模型文件")
        print("预期路径: model/SmartFineTune_*/best.ckpt 或 model/MobileFaceNet_CBAM_*/best.ckpt")
        return
    
    print(f"✅ 基准模型: {base_model}")
    print(f"✅ CBAM模型: {cbam_model}")
    
    # 创建输出目录
    output_dir = Path('./model_comparison_results')
    output_dir.mkdir(exist_ok=True)
    
    # 模拟args对象
    class Args:
        def __init__(self):
            self.rfw_data_dir = rfw_data_dir
            self.device = 'cuda'
            self.output_dir = str(output_dir)
    
    args = Args()
    
    try:
        print(f"\n📁 结果将保存到: {output_dir.absolute()}")
        print("\n🚀 开始比较评估...")
        
        # 运行比较
        run_model_comparison(args)
        
        print(f"\n🎉 比较完成！")
        print(f"📁 详细结果文件:")
        print(f"   - 基准模型结果: baseline_detailed_results.csv")
        print(f"   - CBAM模型结果: cbam_detailed_results.csv")
        print(f"   - 比较结果: model_comparison_results.csv")
        
    except Exception as e:
        print(f"❌ 比较过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
