#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RFW评估脚本使用示例
展示如何使用不同的评估模式
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rfw_evaluation import (
    RFWEvaluator, 
    find_baseline_model, 
    find_cbam_model, 
    run_model_comparison
)


def example_1_evaluate_baseline_model():
    """示例1: 评估基准模型"""
    print("📊 示例1: 评估基准MobileFaceNet模型")
    print("-" * 50)
    
    # 查找基准模型
    model_path = find_baseline_model()
    if not model_path:
        print("❌ 未找到基准模型")
        return
    
    print(f"🔍 使用基准模型: {model_path}")
    
    # 创建评估器
    evaluator = RFWEvaluator(
        model_path=model_path,
        rfw_data_dir='./data/RFW',
        device='cuda',
        model_type='base'
    )
    
    # 评估所有种族
    results = evaluator.evaluate_all_races()
    
    # 生成报告
    evaluator.generate_report(results)
    
    print("✅ 基准模型评估完成\n")


def example_2_evaluate_cbam_model():
    """示例2: 评估CBAM模型"""
    print("📊 示例2: 评估MobileFaceNet+CBAM模型")
    print("-" * 50)
    
    # 查找CBAM模型
    model_path = find_cbam_model()
    if not model_path:
        print("❌ 未找到CBAM模型")
        return
    
    print(f"🔍 使用CBAM模型: {model_path}")
    
    # 创建评估器
    evaluator = RFWEvaluator(
        model_path=model_path,
        rfw_data_dir='./data/RFW',
        device='cuda',
        model_type='cbam'
    )
    
    # 评估所有种族
    results = evaluator.evaluate_all_races()
    
    # 生成报告
    evaluator.generate_report(results)
    
    print("✅ CBAM模型评估完成\n")


def example_3_compare_models():
    """示例3: 比较基准模型和CBAM模型"""
    print("📊 示例3: 比较基准模型和CBAM模型")
    print("-" * 50)
    
    # 检查两个模型是否都存在
    base_model = find_baseline_model()
    cbam_model = find_cbam_model()
    
    if not base_model:
        print("❌ 未找到基准模型")
        return
    if not cbam_model:
        print("❌ 未找到CBAM模型")
        return
    
    print(f"🔍 基准模型: {base_model}")
    print(f"🔍 CBAM模型: {cbam_model}")
    
    # 创建输出目录
    output_dir = Path('./comparison_results')
    output_dir.mkdir(exist_ok=True)
    
    # 模拟args对象
    class Args:
        def __init__(self):
            self.rfw_data_dir = './data/RFW'
            self.device = 'cuda'
            self.output_dir = str(output_dir)
    
    args = Args()
    
    # 运行比较
    run_model_comparison(args)
    
    print("✅ 模型比较完成\n")


def example_4_auto_detect_model():
    """示例4: 自动检测模型类型"""
    print("📊 示例4: 自动检测模型类型")
    print("-" * 50)
    
    # 测试不同的模型路径
    test_models = [
        find_baseline_model(),
        find_cbam_model()
    ]
    
    for model_path in test_models:
        if not model_path:
            continue
            
        print(f"\n🔍 测试模型: {model_path}")
        
        # 创建评估器（自动检测模型类型）
        evaluator = RFWEvaluator(
            model_path=model_path,
            rfw_data_dir='./data/RFW',
            device='cuda',
            model_type='auto'  # 自动检测
        )
        
        print(f"✅ 检测到模型类型: {evaluator.detected_model_type}")
    
    print("\n✅ 自动检测测试完成\n")


def main():
    """主函数"""
    print("🚀 RFW评估脚本使用示例")
    print("=" * 60)
    
    # 检查数据集
    if not os.path.exists('./data/RFW'):
        print("❌ RFW数据集不存在，请确保数据集位于 ./data/RFW")
        return
    
    print("选择要运行的示例:")
    print("1. 评估基准模型（MobileFaceNet）")
    print("2. 评估CBAM模型（MobileFaceNet+CBAM）")
    print("3. 比较基准模型和CBAM模型")
    print("4. 自动检测模型类型测试")
    print("5. 运行所有示例")
    
    try:
        choice = input("请输入选择 (1-5): ").strip()
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return
    
    if choice == '1':
        example_1_evaluate_baseline_model()
    elif choice == '2':
        example_2_evaluate_cbam_model()
    elif choice == '3':
        example_3_compare_models()
    elif choice == '4':
        example_4_auto_detect_model()
    elif choice == '5':
        print("🔄 运行所有示例...\n")
        example_4_auto_detect_model()
        example_1_evaluate_baseline_model()
        example_2_evaluate_cbam_model()
        example_3_compare_models()
    else:
        print("❌ 无效选择")


if __name__ == '__main__':
    main()
