#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RFW (Racial Faces in the Wild) 数据集评估脚本
用于评估预训练的人脸识别模型在四个种族上的性能
支持的种族: Caucasian (高加索人)、Asian (亚洲人)、Indian (印度人)、African (非洲人)
"""

import os
import sys
import argparse
import numpy as np
import torch
import torch.nn.functional as F
import cv2
from pathlib import Path
from tqdm import tqdm
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, auc
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core import model
from core.cbam import CBAM


class BaseMobileFacenet(model.MobileFacenet):
    """基准MobileFaceNet模型（不带CBAM模块）"""

    def __init__(self, bottleneck_setting=model.Mobilefacenet_bottleneck_setting):
        # 调用父类的__init__，但不使用CBAM
        super(model.MobileFacenet, self).__init__()

        self.conv1 = model.ConvBlock(3, 64, 3, 2, 1)
        self.dw_conv1 = model.ConvBlock(64, 64, 3, 1, 1, dw=True)

        self.inplanes = 64
        block = model.Bottleneck
        self.blocks = self._make_layer(block, bottleneck_setting)

        # 不添加CBAM模块
        self.conv2 = model.ConvBlock(128, 512, 1, 1, 0)
        self.linear7 = model.ConvBlock(512, 512, (7, 6), 1, 0, dw=True, linear=True)
        self.linear1 = model.ConvBlock(512, 128, 1, 1, 0, linear=True)

        # 权重初始化
        for m in self.modules():
            if isinstance(m, torch.nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, (2. / n) ** 0.5)
            elif isinstance(m, torch.nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

    def forward(self, x):
        """前向传播（不使用CBAM）"""
        x = self.conv1(x)  # 112x96 -> 56x48
        x = self.dw_conv1(x)  # 56x48 -> 56x48

        # 通过所有bottleneck blocks
        for block in self.blocks:
            x = block(x)

        # 最终卷积层
        x = self.conv2(x)  # 7x6 -> 7x6, 通道数变为512
        x = self.linear7(x)  # 7x6 -> 1x1, 通道数保持512
        x = self.linear1(x)  # 1x1 -> 1x1, 通道数变为128
        x = x.view(x.size(0), -1)

        return x


class RFWEvaluator:
    """RFW数据集评估器"""
    
    def __init__(self, model_path, rfw_data_dir='./data/RFW', device='cuda', model_type='auto'):
        """
        初始化评估器

        Args:
            model_path: 预训练模型路径
            rfw_data_dir: RFW数据集根目录
            device: 计算设备 ('cuda' 或 'cpu')
            model_type: 模型类型 ('auto', 'cbam', 'base')
        """
        self.model_path = model_path
        self.rfw_data_dir = Path(rfw_data_dir)
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.model_type = model_type

        # 四个种族
        self.races = ['African', 'Asian', 'Caucasian', 'Indian']

        # 检测模型类型并加载模型
        self.detected_model_type = self._detect_model_type()
        self.model = self._load_model()

        print(f"🚀 RFW评估器初始化完成")
        print(f"📁 数据目录: {self.rfw_data_dir}")
        print(f"🖥️ 使用设备: {self.device}")
        print(f"🎯 模型路径: {self.model_path}")
        print(f"🔧 模型类型: {self.detected_model_type}")

    def _detect_model_type(self):
        """
        检测模型类型（是否包含CBAM模块）

        Returns:
            str: 'cbam' 或 'base'
        """
        if self.model_type != 'auto':
            return self.model_type

        # 通过模型路径推断类型
        model_path_lower = self.model_path.lower()

        # 基准模型路径特征
        if 'mobilefacenet_best' in model_path_lower or 'baseline' in model_path_lower:
            print("🔍 检测到基准模型路径，使用BaseMobileFacenet")
            return 'base'

        # CBAM模型路径特征
        if 'cbam' in model_path_lower or 'smartfinetune' in model_path_lower:
            print("🔍 检测到CBAM模型路径，使用MobileFacenet+CBAM")
            return 'cbam'

        # 尝试加载模型检查权重
        try:
            checkpoint = torch.load(self.model_path, map_location='cpu')
            state_dict = checkpoint.get('net_state_dict', checkpoint)

            # 检查是否包含CBAM相关的权重
            cbam_keys = [key for key in state_dict.keys() if 'cbam' in key.lower()]

            if cbam_keys:
                print(f"🔍 检测到CBAM权重键: {len(cbam_keys)}个，使用MobileFacenet+CBAM")
                return 'cbam'
            else:
                print("🔍 未检测到CBAM权重，使用BaseMobileFacenet")
                return 'base'

        except Exception as e:
            print(f"⚠️ 模型检测失败: {e}，默认使用CBAM模型")
            return 'cbam'

    def _load_model(self):
        """加载预训练模型"""
        print("📥 加载预训练模型...")

        # 根据检测到的模型类型创建模型
        if self.detected_model_type == 'cbam':
            print("🔧 创建MobileFacenet+CBAM模型")
            net = model.MobileFacenet()
        else:
            print("🔧 创建基准MobileFacenet模型")
            net = BaseMobileFacenet()

        # 加载权重
        if os.path.exists(self.model_path):
            checkpoint = torch.load(self.model_path, map_location=self.device)

            if 'net_state_dict' in checkpoint:
                state_dict = checkpoint['net_state_dict']
                epoch_info = f" (epoch: {checkpoint.get('epoch', 'unknown')})"
            else:
                state_dict = checkpoint
                epoch_info = ""

            # 如果是基准模型，过滤掉CBAM相关的权重
            if self.detected_model_type == 'base':
                filtered_state_dict = {k: v for k, v in state_dict.items()
                                     if 'cbam' not in k.lower()}
                print(f"🔧 过滤CBAM权重: {len(state_dict) - len(filtered_state_dict)} 个权重被跳过")
                state_dict = filtered_state_dict

            # 加载权重（允许部分匹配）
            try:
                net.load_state_dict(state_dict, strict=False)
                print(f"✅ 成功加载{self.detected_model_type.upper()}模型权重{epoch_info}")
            except Exception as e:
                print(f"⚠️ 权重加载警告: {e}")
                # 尝试手动匹配权重
                model_dict = net.state_dict()
                matched_dict = {k: v for k, v in state_dict.items() if k in model_dict}
                model_dict.update(matched_dict)
                net.load_state_dict(model_dict)
                print(f"✅ 部分加载{self.detected_model_type.upper()}模型权重: {len(matched_dict)}/{len(state_dict)}")
        else:
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")

        # 移动到设备并设置为评估模式
        net = net.to(self.device)
        net.eval()

        return net
    
    def _preprocess_image(self, image_path):
        """
        预处理图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            torch.Tensor: 预处理后的图像张量
        """
        # 读取图像
        img = cv2.imread(str(image_path))
        if img is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # BGR转RGB
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # 确保是3通道
        if len(img.shape) == 2:
            img = np.stack([img] * 3, axis=2)
        
        # 归一化到[-1, 1]
        img = (img - 127.5) / 128.0
        
        # 转换为CHW格式
        img = img.transpose(2, 0, 1)
        
        # 转换为tensor
        img_tensor = torch.from_numpy(img).float().unsqueeze(0)
        
        return img_tensor.to(self.device)
    
    def _extract_features(self, image_tensor):
        """
        提取人脸特征
        
        Args:
            image_tensor: 预处理后的图像张量
            
        Returns:
            numpy.ndarray: L2归一化的特征向量
        """
        with torch.no_grad():
            features = self.model(image_tensor)
            # L2归一化
            features = F.normalize(features, p=2, dim=1)
            return features.cpu().numpy()
    
    def _calculate_similarity(self, features1, features2):
        """
        计算特征相似度（余弦相似度）
        
        Args:
            features1: 第一个特征向量
            features2: 第二个特征向量
            
        Returns:
            float: 余弦相似度分数
        """
        # 由于特征已经L2归一化，直接计算点积即为余弦相似度
        similarity = np.dot(features1.flatten(), features2.flatten())
        return float(np.clip(similarity, -1.0, 1.0))
    
    def _parse_pairs_file(self, pairs_file):
        """
        解析pairs.txt文件
        
        Args:
            pairs_file: pairs.txt文件路径
            
        Returns:
            list: 包含(img1_path, img2_path, is_same_person)的元组列表
        """
        pairs = []
        race_name = pairs_file.parent.name  # 获取种族名称
        data_dir = self.rfw_data_dir / 'data' / race_name
        
        with open(pairs_file, 'r') as f:
            lines = f.read().splitlines()
        
        for line in lines:
            parts = line.strip().split('\t')
            
            if len(parts) == 3:
                # 正样本对: person_id img1_id img2_id
                person_id, img1_id, img2_id = parts
                img1_path = data_dir / person_id / f"{person_id}_{int(img1_id):04d}.jpg"
                img2_path = data_dir / person_id / f"{person_id}_{int(img2_id):04d}.jpg"
                is_same_person = True
                
            elif len(parts) == 4:
                # 负样本对: person1_id img1_id person2_id img2_id
                person1_id, img1_id, person2_id, img2_id = parts
                img1_path = data_dir / person1_id / f"{person1_id}_{int(img1_id):04d}.jpg"
                img2_path = data_dir / person2_id / f"{person2_id}_{int(img2_id):04d}.jpg"
                is_same_person = False
            else:
                continue
            
            # 检查文件是否存在
            if img1_path.exists() and img2_path.exists():
                pairs.append((str(img1_path), str(img2_path), is_same_person))
        
        return pairs
    
    def _evaluate_race(self, race):
        """
        评估单个种族的性能
        
        Args:
            race: 种族名称
            
        Returns:
            dict: 评估结果
        """
        print(f"\n🔍 评估 {race} 种族...")
        
        # 解析pairs文件
        pairs_file = self.rfw_data_dir / 'txts' / race / f"{race}_pairs.txt"
        if not pairs_file.exists():
            raise FileNotFoundError(f"Pairs文件不存在: {pairs_file}")
        
        pairs = self._parse_pairs_file(pairs_file)
        print(f"📊 加载了 {len(pairs)} 个图像对")
        
        # 计算相似度
        similarities = []
        labels = []
        
        for img1_path, img2_path, is_same_person in tqdm(pairs, desc=f"处理{race}"):
            try:
                # 预处理图像
                img1_tensor = self._preprocess_image(img1_path)
                img2_tensor = self._preprocess_image(img2_path)
                
                # 提取特征
                features1 = self._extract_features(img1_tensor)
                features2 = self._extract_features(img2_tensor)
                
                # 计算相似度
                similarity = self._calculate_similarity(features1, features2)
                
                similarities.append(similarity)
                labels.append(1 if is_same_person else 0)
                
            except Exception as e:
                print(f"⚠️ 处理图像对时出错: {e}")
                continue
        
        # 计算评估指标
        similarities = np.array(similarities)
        labels = np.array(labels)
        
        # 计算ROC曲线和AUC
        fpr, tpr, thresholds = roc_curve(labels, similarities)
        roc_auc = auc(fpr, tpr)
        
        # 找到最佳阈值（最大化TPR-FPR）
        optimal_idx = np.argmax(tpr - fpr)
        optimal_threshold = thresholds[optimal_idx]
        
        # 计算在最佳阈值下的准确率
        predictions = (similarities >= optimal_threshold).astype(int)
        accuracy = np.mean(predictions == labels) * 100
        
        # 计算其他指标
        tp = np.sum((predictions == 1) & (labels == 1))
        fp = np.sum((predictions == 1) & (labels == 0))
        fn = np.sum((predictions == 0) & (labels == 1))
        
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
        
        results = {
            'race': race,
            'total_pairs': len(pairs),
            'processed_pairs': len(similarities),
            'accuracy': accuracy,
            'auc': roc_auc * 100,
            'optimal_threshold': optimal_threshold,
            'precision': precision * 100,
            'recall': recall * 100,
            'f1_score': f1_score * 100,
            'similarities': similarities,
            'labels': labels,
            'fpr': fpr,
            'tpr': tpr,
            'thresholds': thresholds
        }
        
        print(f"✅ {race} 评估完成:")
        print(f"   📈 准确率: {accuracy:.2f}%")
        print(f"   📊 AUC: {roc_auc*100:.2f}%")
        print(f"   🎯 最佳阈值: {optimal_threshold:.4f}")
        
        return results

    def evaluate_all_races(self):
        """
        评估所有种族的性能

        Returns:
            dict: 所有种族的评估结果
        """
        print("🌍 开始评估所有种族...")

        all_results = {}

        for race in self.races:
            try:
                results = self._evaluate_race(race)
                all_results[race] = results
            except Exception as e:
                print(f"❌ 评估 {race} 时出错: {e}")
                continue

        return all_results

    def _plot_roc_curves(self, all_results, save_path='rfw_roc_curves.png'):
        """
        绘制ROC曲线

        Args:
            all_results: 所有种族的评估结果
            save_path: 保存路径
        """
        plt.figure(figsize=(10, 8))

        colors = ['red', 'blue', 'green', 'orange']

        for i, (race, results) in enumerate(all_results.items()):
            plt.plot(results['fpr'], results['tpr'],
                    color=colors[i], lw=2,
                    label=f'{race} (AUC = {results["auc"]:.2f}%)')

        plt.plot([0, 1], [0, 1], color='gray', lw=1, linestyle='--', alpha=0.5)
        plt.xlim([0.0, 1.0])
        plt.ylim([0.0, 1.05])
        plt.xlabel('False Positive Rate', fontsize=12)
        plt.ylabel('True Positive Rate', fontsize=12)
        plt.title('ROC Curves for Different Races', fontsize=14, fontweight='bold')
        plt.legend(loc="lower right", fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.tight_layout()

        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"📊 ROC曲线已保存到: {save_path}")
        plt.close()

    def _plot_accuracy_comparison(self, all_results, save_path='rfw_accuracy_comparison.png'):
        """
        绘制准确率对比图

        Args:
            all_results: 所有种族的评估结果
            save_path: 保存路径
        """
        races = list(all_results.keys())
        accuracies = [all_results[race]['accuracy'] for race in races]
        aucs = [all_results[race]['auc'] for race in races]

        _, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 准确率柱状图
        bars1 = ax1.bar(races, accuracies, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A'])
        ax1.set_ylabel('Accuracy (%)', fontsize=12)
        ax1.set_title('Face Verification Accuracy by Race', fontsize=14, fontweight='bold')
        ax1.set_ylim(0, 100)

        # 在柱状图上添加数值标签
        for bar, acc in zip(bars1, accuracies):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{acc:.2f}%', ha='center', va='bottom', fontweight='bold')

        # AUC柱状图
        bars2 = ax2.bar(races, aucs, color=['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A'])
        ax2.set_ylabel('AUC (%)', fontsize=12)
        ax2.set_title('AUC Score by Race', fontsize=14, fontweight='bold')
        ax2.set_ylim(0, 100)

        # 在柱状图上添加数值标签
        for bar, auc_val in zip(bars2, aucs):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{auc_val:.2f}%', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"📊 准确率对比图已保存到: {save_path}")
        plt.close()

    def _save_detailed_results(self, all_results, save_path='rfw_detailed_results.csv'):
        """
        保存详细结果到CSV文件

        Args:
            all_results: 所有种族的评估结果
            save_path: 保存路径
        """
        data = []
        for race, results in all_results.items():
            data.append({
                'Race': race,
                'Total_Pairs': results['total_pairs'],
                'Processed_Pairs': results['processed_pairs'],
                'Accuracy (%)': f"{results['accuracy']:.2f}",
                'AUC (%)': f"{results['auc']:.2f}",
                'Precision (%)': f"{results['precision']:.2f}",
                'Recall (%)': f"{results['recall']:.2f}",
                'F1_Score (%)': f"{results['f1_score']:.2f}",
                'Optimal_Threshold': f"{results['optimal_threshold']:.4f}"
            })

        df = pd.DataFrame(data)
        df.to_csv(save_path, index=False)
        print(f"📄 详细结果已保存到: {save_path}")

    def generate_report(self, all_results):
        """
        生成评估报告

        Args:
            all_results: 所有种族的评估结果
        """
        print("\n" + "="*80)
        print("🎯 RFW数据集评估报告")
        print("="*80)

        # 总体统计
        total_pairs = sum(results['processed_pairs'] for results in all_results.values())
        avg_accuracy = np.mean([results['accuracy'] for results in all_results.values()])
        avg_auc = np.mean([results['auc'] for results in all_results.values()])

        print(f"\n📊 总体统计:")
        print(f"   处理的图像对总数: {total_pairs:,}")
        print(f"   平均准确率: {avg_accuracy:.2f}%")
        print(f"   平均AUC: {avg_auc:.2f}%")

        # 各种族详细结果
        print(f"\n📈 各种族详细结果:")
        print("-" * 80)
        print(f"{'种族':<12} {'准确率':<10} {'AUC':<10} {'精确率':<10} {'召回率':<10} {'F1分数':<10}")
        print("-" * 80)

        for race, results in all_results.items():
            print(f"{race:<12} {results['accuracy']:<10.2f} {results['auc']:<10.2f} "
                  f"{results['precision']:<10.2f} {results['recall']:<10.2f} {results['f1_score']:<10.2f}")

        # 性能差异分析
        accuracies = [results['accuracy'] for results in all_results.values()]
        max_acc = max(accuracies)
        min_acc = min(accuracies)
        acc_std = np.std(accuracies)

        print(f"\n🔍 性能差异分析:")
        print(f"   最高准确率: {max_acc:.2f}%")
        print(f"   最低准确率: {min_acc:.2f}%")
        print(f"   准确率差异: {max_acc - min_acc:.2f}%")
        print(f"   准确率标准差: {acc_std:.2f}%")

        if acc_std > 5.0:
            print("   ⚠️  不同种族间存在较大性能差异，建议进一步优化模型")
        else:
            print("   ✅ 不同种族间性能相对均衡")

        print("="*80)


def find_best_model(prefer_cbam=True):
    """
    查找最佳预训练模型

    Args:
        prefer_cbam: 是否优先选择CBAM模型

    Returns:
        str: 模型路径
    """
    # CBAM模型路径
    cbam_paths = [
        'model/SmartFineTune_20250627_211845/best.ckpt',
        'model/MobileFaceNet_CBAM_20250530_163040/best.ckpt',
        'model/MobileFaceNet_CBAM_20250530_164030/best.ckpt'
    ]

    # 基准模型路径
    base_paths = [
        'model/mobilefacenet_best/068.ckpt',
        'model/best/068.ckpt'
    ]

    # 根据偏好选择搜索顺序
    if prefer_cbam:
        possible_paths = cbam_paths + base_paths
    else:
        possible_paths = base_paths + cbam_paths

    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 搜索model目录下的所有best.ckpt文件
    model_dir = Path('model')
    if model_dir.exists():
        best_files = list(model_dir.rglob('best.ckpt'))
        if best_files:
            return str(best_files[0])

        # 如果没有best.ckpt，查找其他.ckpt文件
        ckpt_files = list(model_dir.rglob('*.ckpt'))
        if ckpt_files:
            return str(ckpt_files[0])

    return None


def find_baseline_model():
    """查找基准模型（不带CBAM）"""
    return find_best_model(prefer_cbam=False)


def find_cbam_model():
    """查找CBAM模型"""
    return find_best_model(prefer_cbam=True)


def run_model_comparison(args):
    """运行模型比较"""
    print("🔄 开始比较基准模型和CBAM模型...")

    # 查找两种模型
    base_model_path = find_baseline_model()
    cbam_model_path = find_cbam_model()

    if not base_model_path:
        print("❌ 未找到基准模型")
        return
    if not cbam_model_path:
        print("❌ 未找到CBAM模型")
        return

    print(f"📊 基准模型: {base_model_path}")
    print(f"📊 CBAM模型: {cbam_model_path}")

    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)

    results_comparison = {}

    # 评估基准模型
    try:
        print("\n" + "="*60)
        print("🔍 评估基准模型 (MobileFaceNet)")
        print("="*60)

        base_evaluator = RFWEvaluator(
            model_path=base_model_path,
            rfw_data_dir=args.rfw_data_dir,
            device=args.device,
            model_type='base'
        )

        base_results = base_evaluator.evaluate_all_races()
        results_comparison['baseline'] = base_results

        # 保存基准模型结果
        base_evaluator._save_detailed_results(base_results,
                                            save_path=output_dir / 'baseline_detailed_results.csv')

    except Exception as e:
        print(f"❌ 基准模型评估失败: {e}")
        return

    # 评估CBAM模型
    try:
        print("\n" + "="*60)
        print("🔍 评估CBAM模型 (MobileFaceNet+CBAM)")
        print("="*60)

        cbam_evaluator = RFWEvaluator(
            model_path=cbam_model_path,
            rfw_data_dir=args.rfw_data_dir,
            device=args.device,
            model_type='cbam'
        )

        cbam_results = cbam_evaluator.evaluate_all_races()
        results_comparison['cbam'] = cbam_results

        # 保存CBAM模型结果
        cbam_evaluator._save_detailed_results(cbam_results,
                                            save_path=output_dir / 'cbam_detailed_results.csv')

    except Exception as e:
        print(f"❌ CBAM模型评估失败: {e}")
        return

    # 生成比较报告
    generate_comparison_report(results_comparison, output_dir)


def generate_comparison_report(results_comparison, output_dir):
    """生成模型比较报告"""
    print("\n" + "="*80)
    print("📊 模型性能比较报告")
    print("="*80)

    base_results = results_comparison['baseline']
    cbam_results = results_comparison['cbam']

    # 比较表格
    print(f"\n{'种族':<12} {'基准准确率':<12} {'CBAM准确率':<12} {'提升':<10} {'基准AUC':<10} {'CBAM AUC':<10} {'AUC提升':<10}")
    print("-" * 80)

    total_base_acc = 0
    total_cbam_acc = 0
    total_base_auc = 0
    total_cbam_auc = 0

    for race in ['African', 'Asian', 'Caucasian', 'Indian']:
        if race in base_results and race in cbam_results:
            base_acc = base_results[race]['accuracy']
            cbam_acc = cbam_results[race]['accuracy']
            acc_improvement = cbam_acc - base_acc

            base_auc = base_results[race]['auc']
            cbam_auc = cbam_results[race]['auc']
            auc_improvement = cbam_auc - base_auc

            print(f"{race:<12} {base_acc:<12.2f} {cbam_acc:<12.2f} {acc_improvement:<10.2f} "
                  f"{base_auc:<10.2f} {cbam_auc:<10.2f} {auc_improvement:<10.2f}")

            total_base_acc += base_acc
            total_cbam_acc += cbam_acc
            total_base_auc += base_auc
            total_cbam_auc += cbam_auc

    # 平均值
    avg_base_acc = total_base_acc / 4
    avg_cbam_acc = total_cbam_acc / 4
    avg_acc_improvement = avg_cbam_acc - avg_base_acc

    avg_base_auc = total_base_auc / 4
    avg_cbam_auc = total_cbam_auc / 4
    avg_auc_improvement = avg_cbam_auc - avg_base_auc

    print("-" * 80)
    print(f"{'平均':<12} {avg_base_acc:<12.2f} {avg_cbam_acc:<12.2f} {avg_acc_improvement:<10.2f} "
          f"{avg_base_auc:<10.2f} {avg_cbam_auc:<10.2f} {avg_auc_improvement:<10.2f}")

    # 总结
    print(f"\n📈 总体性能提升:")
    print(f"   准确率提升: {avg_acc_improvement:.2f}%")
    print(f"   AUC提升: {avg_auc_improvement:.2f}%")

    if avg_acc_improvement > 0:
        print(f"   ✅ CBAM模块显著提升了模型性能")
    else:
        print(f"   ⚠️ CBAM模块未能提升模型性能")

    # 保存比较结果
    comparison_data = []
    for race in ['African', 'Asian', 'Caucasian', 'Indian']:
        if race in base_results and race in cbam_results:
            comparison_data.append({
                'Race': race,
                'Baseline_Accuracy': f"{base_results[race]['accuracy']:.2f}",
                'CBAM_Accuracy': f"{cbam_results[race]['accuracy']:.2f}",
                'Accuracy_Improvement': f"{cbam_results[race]['accuracy'] - base_results[race]['accuracy']:.2f}",
                'Baseline_AUC': f"{base_results[race]['auc']:.2f}",
                'CBAM_AUC': f"{cbam_results[race]['auc']:.2f}",
                'AUC_Improvement': f"{cbam_results[race]['auc'] - base_results[race]['auc']:.2f}"
            })

    # 添加平均值行
    comparison_data.append({
        'Race': 'Average',
        'Baseline_Accuracy': f"{avg_base_acc:.2f}",
        'CBAM_Accuracy': f"{avg_cbam_acc:.2f}",
        'Accuracy_Improvement': f"{avg_acc_improvement:.2f}",
        'Baseline_AUC': f"{avg_base_auc:.2f}",
        'CBAM_AUC': f"{avg_cbam_auc:.2f}",
        'AUC_Improvement': f"{avg_auc_improvement:.2f}"
    })

    df = pd.DataFrame(comparison_data)
    df.to_csv(output_dir / 'model_comparison_results.csv', index=False)
    print(f"\n📄 比较结果已保存到: {output_dir / 'model_comparison_results.csv'}")

    print("="*80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='RFW数据集评估脚本')
    parser.add_argument('--model_path', type=str, default=None,
                       help='预训练模型路径')
    parser.add_argument('--model_type', type=str, default='auto',
                       choices=['auto', 'cbam', 'base'],
                       help='模型类型: auto(自动检测), cbam(带CBAM), base(基准模型)')
    parser.add_argument('--rfw_data_dir', type=str, default='./data/RFW',
                       help='RFW数据集根目录')
    parser.add_argument('--device', type=str, default='cuda',
                       help='计算设备 (cuda/cpu)')
    parser.add_argument('--output_dir', type=str, default='./rfw_results',
                       help='结果输出目录')
    parser.add_argument('--compare_models', action='store_true',
                       help='比较基准模型和CBAM模型的性能')

    args = parser.parse_args()

    # 如果要比较模型，运行比较模式
    if args.compare_models:
        run_model_comparison(args)
        return

    # 查找模型路径
    if args.model_path is None:
        if args.model_type == 'base':
            args.model_path = find_baseline_model()
        elif args.model_type == 'cbam':
            args.model_path = find_cbam_model()
        else:
            args.model_path = find_best_model()

        if args.model_path is None:
            print("❌ 未找到预训练模型，请指定 --model_path 参数")
            return
        print(f"🔍 自动找到模型: {args.model_path}")

    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)

    try:
        # 创建评估器
        evaluator = RFWEvaluator(
            model_path=args.model_path,
            rfw_data_dir=args.rfw_data_dir,
            device=args.device,
            model_type=args.model_type
        )

        # 评估所有种族
        all_results = evaluator.evaluate_all_races()

        if not all_results:
            print("❌ 没有成功评估任何种族")
            return

        # 生成可视化结果
        evaluator._plot_roc_curves(all_results,
                                 save_path=output_dir / 'rfw_roc_curves.png')
        evaluator._plot_accuracy_comparison(all_results,
                                          save_path=output_dir / 'rfw_accuracy_comparison.png')

        # 保存详细结果
        evaluator._save_detailed_results(all_results,
                                       save_path=output_dir / 'rfw_detailed_results.csv')

        # 生成报告
        evaluator.generate_report(all_results)

        print(f"\n🎉 评估完成！结果已保存到: {output_dir}")

    except Exception as e:
        print(f"❌ 评估过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
