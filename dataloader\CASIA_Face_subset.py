import numpy as np
import os
import torch
import cv2
import random
from collections import defaultdict

class CASIA_Face_Subset(object):
    def __init__(self, root, num_identities=500, min_images_per_identity=20, seed=42):
        """
        创建CASIA-WebFace子集，随机选择指定数量的身份，每个身份至少有指定数量的图片
        
        Args:
            root: 数据集根目录
            num_identities: 要选择的身份数量
            min_images_per_identity: 每个身份至少需要的图片数量
            seed: 随机种子，确保可重复性
        """
        self.root = root
        self.num_identities = num_identities
        self.min_images_per_identity = min_images_per_identity
        
        # 设置随机种子
        random.seed(seed)
        np.random.seed(seed)
        
        # 读取原始数据集信息
        img_txt_dir = os.path.join(root, 'CASIA-WebFace-112X96.txt')
        
        # 收集每个身份的所有图片
        identity_to_images = defaultdict(list)
        identity_to_label = {}
        
        with open(img_txt_dir) as f:
            img_label_list = f.read().splitlines()
        
        for info in img_label_list:
            image_dir, label_name = info.split(' ')
            label_id = int(label_name)
            identity_to_images[label_id].append(os.path.join(root, 'CASIA-WebFace-112X96', image_dir))
            identity_to_label[label_id] = label_id
        
        # 筛选至少有min_images_per_identity张图片的身份
        qualified_identities = [identity for identity, images in identity_to_images.items() 
                              if len(images) >= min_images_per_identity]
        
        print(f"原始数据集中共有 {len(identity_to_images)} 个身份")
        print(f"符合条件（至少 {min_images_per_identity} 张图片）的身份有 {len(qualified_identities)} 个")
        
        # 如果符合条件的身份数量不足，发出警告
        if len(qualified_identities) < num_identities:
            print(f"警告：符合条件的身份数量({len(qualified_identities)})少于请求的数量({num_identities})！")
            print(f"将使用所有符合条件的身份。")
            selected_identities = qualified_identities
        else:
            # 随机选择num_identities个身份
            selected_identities = random.sample(qualified_identities, num_identities)
        
        # 创建新的映射
        self.image_list = []
        self.label_list = []
        self.selected_identities = {}
        
        # 为选中的身份创建新的标签映射（从0开始的连续整数）
        for new_label, original_identity in enumerate(selected_identities):
            self.selected_identities[original_identity] = new_label
            images = identity_to_images[original_identity]
            self.image_list.extend(images)
            self.label_list.extend([new_label] * len(images))
        
        # 打乱数据集
        combined = list(zip(self.image_list, self.label_list))
        random.shuffle(combined)
        self.image_list, self.label_list = zip(*combined)
        
        self.class_nums = len(selected_identities)
        
        print(f"子集创建完成，包含 {self.class_nums} 个身份，共 {len(self.image_list)} 张图片")
    
    def get_identity_stats(self):
        """返回子集中每个身份的图片数量统计"""
        stats = defaultdict(int)
        for label in self.label_list:
            stats[label] += 1
        return dict(stats)
    
    def __getitem__(self, index):
        img_path = self.image_list[index]
        target = self.label_list[index]
        
        # 使用cv2读取图像
        img = cv2.imread(img_path)
        if img is None:
            raise ValueError(f"无法读取图像：{img_path}")
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # 转换颜色空间

        if len(img.shape) == 2:
            img = np.stack([img] * 3, 2)
        
        # 随机水平翻转
        flip = np.random.choice(2)*2-1
        img = img[:, ::flip, :]
        
        # 标准化处理
        img = (img - 127.5) / 128.0
        img = img.transpose(2, 0, 1)
        img = torch.from_numpy(img).float()

        return img, target

    def __len__(self):
        return len(self.image_list)


if __name__ == '__main__':
    data_dir = '/path/to/your/dataset'
    dataset = CASIA_Face_Subset(root=data_dir, num_identities=500, min_images_per_identity=20)
    
    # 输出子集统计信息
    stats = dataset.get_identity_stats()
    min_images = min(stats.values())
    max_images = max(stats.values())
    avg_images = sum(stats.values()) / len(stats)
    
    print(f"每个身份的图片数量 - 最少: {min_images}, 最多: {max_images}, 平均: {avg_images:.2f}")
    
    # 测试数据加载
    trainloader = torch.utils.data.DataLoader(dataset, batch_size=32, shuffle=True, num_workers=8, drop_last=False)
    print(f"子集大小: {len(dataset)}")
    for i, data in enumerate(trainloader):
        if i == 0:
            print(f"批次数据形状: {data[0].shape}")
            break 