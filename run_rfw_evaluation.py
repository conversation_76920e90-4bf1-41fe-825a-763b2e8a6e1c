#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RFW数据集评估运行脚本
简化版本，自动查找模型并运行评估
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rfw_evaluation import RFWEvaluator, find_best_model, find_baseline_model, find_cbam_model, run_model_comparison


def main():
    """主函数"""
    print("🚀 启动RFW数据集评估...")
    print("选择评估模式:")
    print("1. 评估单个模型（自动检测）")
    print("2. 评估基准模型（MobileFaceNet）")
    print("3. 评估CBAM模型（MobileFaceNet+CBAM）")
    print("4. 比较基准模型和CBAM模型")

    try:
        choice = input("请输入选择 (1-4，默认1): ").strip()
        if not choice:
            choice = '1'
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        return

    # 检查数据集是否存在
    rfw_data_dir = './data/RFW'
    if not os.path.exists(rfw_data_dir):
        print(f"❌ RFW数据集不存在: {rfw_data_dir}")
        print("请确保RFW数据集已正确放置在data/RFW目录下")
        return

    # 创建输出目录
    output_dir = Path('./rfw_results')
    output_dir.mkdir(exist_ok=True)

    if choice == '4':
        # 比较模式
        print("\n🔄 启动模型比较模式...")

        # 模拟args对象
        class Args:
            def __init__(self):
                self.rfw_data_dir = rfw_data_dir
                self.device = 'cuda'
                self.output_dir = str(output_dir)

        args = Args()
        run_model_comparison(args)
        return

    # 单模型评估模式
    if choice == '2':
        model_path = find_baseline_model()
        model_type = 'base'
        model_name = "基准模型（MobileFaceNet）"
    elif choice == '3':
        model_path = find_cbam_model()
        model_type = 'cbam'
        model_name = "CBAM模型（MobileFaceNet+CBAM）"
    else:
        model_path = find_best_model()
        model_type = 'auto'
        model_name = "自动检测模型"

    if model_path is None:
        print("❌ 未找到预训练模型")
        print("请确保model目录下存在训练好的.ckpt文件")
        return

    print(f"🔍 使用{model_name}: {model_path}")

    try:
        # 创建评估器
        evaluator = RFWEvaluator(
            model_path=model_path,
            rfw_data_dir=rfw_data_dir,
            device='cuda',  # 如果没有GPU会自动切换到CPU
            model_type=model_type
        )
        
        # 评估所有种族
        print("\n🌍 开始评估四个种族的人脸识别性能...")
        all_results = evaluator.evaluate_all_races()
        
        if not all_results:
            print("❌ 没有成功评估任何种族")
            return
        
        # 生成可视化结果
        print("\n📊 生成可视化结果...")
        evaluator._plot_roc_curves(all_results, 
                                 save_path=output_dir / 'rfw_roc_curves.png')
        evaluator._plot_accuracy_comparison(all_results, 
                                          save_path=output_dir / 'rfw_accuracy_comparison.png')
        
        # 保存详细结果
        evaluator._save_detailed_results(all_results, 
                                       save_path=output_dir / 'rfw_detailed_results.csv')
        
        # 生成报告
        evaluator.generate_report(all_results)
        
        print(f"\n🎉 评估完成！")
        print(f"📁 结果文件保存在: {output_dir.absolute()}")
        print(f"   - ROC曲线图: rfw_roc_curves.png")
        print(f"   - 准确率对比图: rfw_accuracy_comparison.png") 
        print(f"   - 详细结果CSV: rfw_detailed_results.csv")
        
    except Exception as e:
        print(f"❌ 评估过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
