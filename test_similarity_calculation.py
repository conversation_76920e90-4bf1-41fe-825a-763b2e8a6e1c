#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试相似度计算逻辑
验证特征提取和相似度计算是否正确

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import torch
import torch.nn.functional as F
import numpy as np
import cv2
from PIL import Image

# 解决OpenMP冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core import model

def preprocess_image(image_path):
    """预处理图像"""
    try:
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # BGR转RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 调整大小到112x96
        image = cv2.resize(image, (96, 112))
        
        # 转换为PIL图像并归一化
        image = Image.fromarray(image)
        
        # 转换为张量并归一化到[-1, 1]
        image = np.array(image).astype(np.float32)
        image = (image - 127.5) / 128.0
        
        # 转换为CHW格式
        image = np.transpose(image, (2, 0, 1))
        
        # 转换为PyTorch张量
        image_tensor = torch.from_numpy(image).unsqueeze(0)
        
        return image_tensor
        
    except Exception as e:
        print(f"❌ 图像预处理失败 {image_path}: {e}")
        return None

def extract_features(model_net, image_tensor, device):
    """提取特征"""
    model_net.eval()
    with torch.no_grad():
        image_tensor = image_tensor.to(device)
        features = model_net(image_tensor)
        # L2归一化
        features = F.normalize(features, p=2, dim=1)
        return features.cpu().numpy()

def calculate_similarity(features1, features2):
    """计算余弦相似度"""
    similarity = np.dot(features1.flatten(), features2.flatten())
    return float(np.clip(similarity, -1.0, 1.0))

def test_similarity_logic():
    """测试相似度计算逻辑"""
    print("🧪 测试相似度计算逻辑")
    print("="*50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🔧 使用设备: {device}")
    
    # 测试不同模型
    models_to_test = [
        ("基础模型", "model/best/068.ckpt"),
        ("SmartFineTune模型", "model/SmartFineTune_20250627_211845/best.ckpt")
    ]
    
    # 测试图片对
    test_pairs = [
        ("同一人-黄渤", "data/makeup_dataset/huangbo/bo.png", "data/makeup_dataset/huangbo/bo1.png", True),
        ("不同人-黄渤vs梁家辉", "data/makeup_dataset/huangbo/bo.png", "data/makeup_dataset/huangbo/liang1.png", False),
        ("同一人-梁家辉", "data/makeup_dataset/liangjiahui/liang.png", "data/makeup_dataset/liangjiahui/liang1.png", True),
        ("不同人-梁家辉vs黄渤", "data/makeup_dataset/liangjiahui/liang.png", "data/makeup_dataset/liangjiahui/liang4-bo2.png", False),
    ]
    
    for model_name, model_path in models_to_test:
        print(f"\n🤖 测试模型: {model_name}")
        print(f"   路径: {model_path}")
        
        if not os.path.exists(model_path):
            print(f"   ⚠️  模型文件不存在，跳过")
            continue
        
        # 加载模型
        net = model.MobileFacenet()
        net = net.to(device)
        
        try:
            checkpoint = torch.load(model_path, map_location=device)
            if 'net_state_dict' in checkpoint:
                state_dict = checkpoint['net_state_dict']
            elif 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint
            
            net.load_state_dict(state_dict, strict=False)
            print(f"   ✅ 模型加载成功")
        except Exception as e:
            print(f"   ❌ 模型加载失败: {e}")
            continue
        
        # 测试图片对
        similarities = []
        for pair_name, img1_path, img2_path, is_same_person in test_pairs:
            if not (os.path.exists(img1_path) and os.path.exists(img2_path)):
                print(f"   ⚠️  图片不存在，跳过: {pair_name}")
                continue
            
            # 预处理图像
            img1_tensor = preprocess_image(img1_path)
            img2_tensor = preprocess_image(img2_path)
            
            if img1_tensor is None or img2_tensor is None:
                print(f"   ⚠️  图像预处理失败，跳过: {pair_name}")
                continue
            
            # 提取特征
            features1 = extract_features(net, img1_tensor, device)
            features2 = extract_features(net, img2_tensor, device)
            
            # 计算相似度
            similarity = calculate_similarity(features1, features2)
            similarities.append(similarity)
            
            # 显示结果
            status = "✅" if ((similarity > 0.5) == is_same_person) else "❌"
            label = "同一人" if is_same_person else "不同人"
            print(f"   {status} {pair_name}: 相似度={similarity:.4f} ({label})")
        
        # 统计信息
        if similarities:
            print(f"   📊 相似度统计:")
            print(f"      平均值: {np.mean(similarities):.4f}")
            print(f"      标准差: {np.std(similarities):.4f}")
            print(f"      范围: [{np.min(similarities):.4f}, {np.max(similarities):.4f}]")
    
    print(f"\n🎉 测试完成!")

def test_feature_properties():
    """测试特征向量的性质"""
    print("\n🔬 测试特征向量性质")
    print("="*50)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 加载一个模型
    model_path = "model/SmartFineTune_20250627_211845/best.ckpt"
    if not os.path.exists(model_path):
        model_path = "model/best/068.ckpt"
    
    if not os.path.exists(model_path):
        print("❌ 找不到可用的模型文件")
        return
    
    net = model.MobileFacenet()
    net = net.to(device)
    
    try:
        checkpoint = torch.load(model_path, map_location=device)
        if 'net_state_dict' in checkpoint:
            state_dict = checkpoint['net_state_dict']
        else:
            state_dict = checkpoint
        net.load_state_dict(state_dict, strict=False)
        print(f"✅ 模型加载成功: {model_path}")
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return
    
    # 测试一张图片
    test_image = "data/makeup_dataset/huangbo/bo.png"
    if not os.path.exists(test_image):
        print(f"❌ 测试图片不存在: {test_image}")
        return
    
    img_tensor = preprocess_image(test_image)
    if img_tensor is None:
        print("❌ 图像预处理失败")
        return
    
    features = extract_features(net, img_tensor, device)
    
    print(f"📊 特征向量分析:")
    print(f"   形状: {features.shape}")
    print(f"   数据类型: {features.dtype}")
    print(f"   L2范数: {np.linalg.norm(features):.6f}")
    print(f"   均值: {np.mean(features):.6f}")
    print(f"   标准差: {np.std(features):.6f}")
    print(f"   最小值: {np.min(features):.6f}")
    print(f"   最大值: {np.max(features):.6f}")
    
    # 测试自相似度（应该为1）
    self_similarity = calculate_similarity(features, features)
    print(f"   自相似度: {self_similarity:.6f} (应该为1.0)")
    
    # 测试随机向量的相似度
    random_features = np.random.randn(*features.shape).astype(np.float32)
    random_features = random_features / np.linalg.norm(random_features)  # L2归一化
    random_similarity = calculate_similarity(features, random_features)
    print(f"   与随机向量相似度: {random_similarity:.6f} (应该接近0)")

if __name__ == "__main__":
    test_similarity_logic()
    test_feature_properties()
