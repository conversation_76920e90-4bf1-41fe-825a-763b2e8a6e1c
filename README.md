# MobileFaceNet-CBAM

基于MobileFaceNet实现的人脸识别模型，集成了CBAM注意力机制，提供完整的训练、微调和可视化功能。

## 项目特性

- 🚀 基于轻量级MobileFaceNet架构
- 🎯 集成CBAM注意力机制提升识别精度
- 📊 支持CASIA-WebFace和LFW数据集
- 🔍 提供注意力可视化工具
- ⚡ 支持GPU加速训练和推理
- 📦 支持数据集子集训练

## 环境要求

- Python 3.6+
- PyTorch 1.8.0+
- CUDA 10.2+ (可选，用于GPU加速)

## 安装

1. 克隆项目：
```bash
git clone <repository-url>
cd MobileFaceNet_Pytorch-master
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

## 🚀 快速开始

### 智能微调（推荐）
```bash
# 标准配置训练（自动检测模型和数据）
python smart_finetune.py --preset standard

# 快速测试环境
python smart_finetune.py --preset quick

# 高质量训练
python smart_finetune.py --preset high_quality
```

### 注意力可视化
```bash
# 单张图像可视化
python attention_heatmap_visualizer.py --image your_image.jpg --model SmartFineTune_*/best.ckpt

# 批量处理
python attention_heatmap_visualizer.py --batch --input_dir ./images --model SmartFineTune_*/best.ckpt
```

## 📚 完整文档

**⭐ 请查看 [完整使用指南](MOBILEFACENET_CBAM_COMPLETE_GUIDE.md) 获取详细说明！**

包含以下内容：
- 🎯 智能微调系统（预设配置、自动检测、参数覆盖）
- 🎨 注意力可视化系统（多阶段CBAM热力图）
- 📊 数据集准备和处理
- 🔄 完整工作流程
- 🔧 故障排除和性能优化

## 传统方式（不推荐）

### 1. 数据准备
将CASIA-WebFace数据集放置在 `./data/CASIA/` 目录下，LFW数据集放置在 `./data/lfw/` 目录下。

### 2. 训练模型
```bash
python train.py
```

### 3. 微调CBAM模型
```bash
python finetune.py --pretrained ./model/best/068.ckpt --epochs 20 --lr 0.0001
```

### 4. 模型评估

```bash
python lfw_eval.py --resume ./model/best/model.ckpt
```

### 5. 注意力可视化

单张图片：
```bash
python visualize_attention.py --model ./model/best/model.ckpt --image ./data/test.jpg --output ./result/attention.png
```

批量处理：
```bash
python batch_visualize.py --model ./model/best/model.ckpt --input-dir ./data/test_images --output-dir ./result/attention_maps
```

## 项目结构

```
├── core/                   # 核心模块
│   ├── model.py           # 模型定义
│   ├── cbam.py            # CBAM注意力模块
│   └── utils.py           # 工具函数
├── dataloader/            # 数据加载器
│   ├── CASIA_Face_loader.py
│   ├── LFW_loader.py
│   └── CASIA_Face_subset_loader.py
├── data/                  # 数据目录
├── model/                 # 模型保存目录
├── result/                # 结果输出目录
├── config.py              # 配置文件
├── train.py               # 训练脚本
├── finetune.py            # 微调脚本
├── lfw_eval.py            # LFW评估脚本
├── visualize_attention.py # 注意力可视化
├── batch_visualize.py     # 批量可视化
└── requirements.txt       # 依赖列表
```

## 配置说明

主要配置项在 `config.py` 中：

- `BATCH_SIZE`: 批量大小
- `TOTAL_EPOCH`: 训练轮数
- `CASIA_DATA_DIR`: CASIA数据集路径
- `LFW_DATA_DIR`: LFW数据集路径
- `GPU`: 使用的GPU设备

## 详细文档

- [CBAM模块说明](README_CBAM.md)
- [可视化工具使用](README_VISUALIZATION.md)

## 许可证

本项目基于MIT许可证开源。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 引用

如果本项目对您的研究有帮助，请考虑引用：

```bibtex
@misc{mobilefacenet-cbam,
  title={MobileFaceNet with CBAM Attention Mechanism},
  author={Your Name},
  year={2024},
  url={https://github.com/your-repo}
}
```
