#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
影视明星不同妆造形象人脸验证脚本
用于执行1:1人脸比对任务，计算验证准确率和特征相似度

数据集结构：
data/makeup_dataset/
├── celebrity1/
│   ├── celebrity1.png (生活照)
│   ├── celebrity1_1.png (剧照1)
│   ├── celebrity1_2.png (剧照2)
│   └── ...
└── celebrity2/
    ├── celebrity2.png (生活照)
    └── ...

日期: 2025-06-30

python celebrity_makeup_verification.py --dataset data/makeup_dataset --model model/SmartFineTune_20250627_211845/best.ckpt --threshold 0.6

"""

import os
import sys

# 解决OpenMP冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

import torch
import torch.nn.functional as F
import numpy as np
import cv2
from PIL import Image
import argparse
from tqdm import tqdm
from datetime import datetime
import pandas as pd
import csv

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core import model
from core.cbam import CBAM


class CelebrityMakeupVerifier:
    """影视明星妆造验证器"""
    
    def __init__(self, model_path, device='cuda'):
        """
        初始化验证器
        
        Args:
            model_path: 预训练模型路径
            device: 计算设备 ('cuda' 或 'cpu')
        """
        self.device = torch.device(device if torch.cuda.is_available() else 'cpu')
        self.model = self._load_model(model_path)
        self.model.eval()
        
        print(f"✅ 模型加载成功: {model_path}")
        print(f"🔧 使用设备: {self.device}")
    
    def _load_model(self, model_path):
        """加载预训练模型"""
        # 创建模型
        net = model.MobileFacenet()
        net = net.to(self.device)
        
        if os.path.exists(model_path):
            print(f"📂 加载模型权重: {model_path}")
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # 处理不同的检查点格式
            if 'net_state_dict' in checkpoint:
                state_dict = checkpoint['net_state_dict']
            elif 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            else:
                state_dict = checkpoint
            
            # 加载模型参数
            try:
                net.load_state_dict(state_dict, strict=False)
                print("✅ 模型参数加载成功")
            except Exception as e:
                print(f"⚠️  模型参数加载警告: {e}")
                # 尝试部分加载
                model_dict = net.state_dict()
                pretrained_dict = {k: v for k, v in state_dict.items() if k in model_dict}
                model_dict.update(pretrained_dict)
                net.load_state_dict(model_dict)
                print(f"✅ 部分参数加载成功: {len(pretrained_dict)}/{len(model_dict)}")
        else:
            print(f"⚠️  模型文件不存在，使用随机初始化的模型: {model_path}")
        
        return net
    
    def _preprocess_image(self, image_path):
        """
        图像预处理
        
        Args:
            image_path: 图像路径
            
        Returns:
            torch.Tensor: 预处理后的图像张量
        """
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"无法读取图像: {image_path}")
            
            # BGR转RGB
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 调整大小到112x96 (MobileFaceNet的输入尺寸)
            image = cv2.resize(image, (96, 112))
            
            # 转换为PIL图像并归一化
            image = Image.fromarray(image)
            
            # 转换为张量并归一化到[-1, 1]
            image = np.array(image).astype(np.float32)
            image = (image - 127.5) / 128.0
            
            # 转换为CHW格式
            image = np.transpose(image, (2, 0, 1))
            
            # 转换为PyTorch张量
            image_tensor = torch.from_numpy(image).unsqueeze(0).to(self.device)
            
            return image_tensor
            
        except Exception as e:
            print(f"❌ 图像预处理失败 {image_path}: {e}")
            return None
    
    def _extract_features(self, image_tensor):
        """
        提取人脸特征
        
        Args:
            image_tensor: 预处理后的图像张量
            
        Returns:
            numpy.ndarray: 归一化的特征向量
        """
        with torch.no_grad():
            features = self.model(image_tensor)
            # L2归一化
            features = F.normalize(features, p=2, dim=1)
            return features.cpu().numpy()
    
    def _calculate_similarity(self, features1, features2):
        """
        计算特征相似度（余弦相似度）

        Args:
            features1: 第一个特征向量 (已L2归一化)
            features2: 第二个特征向量 (已L2归一化)

        Returns:
            float: 余弦相似度分数 [-1, 1]，值越大越相似
        """
        # 由于特征已经L2归一化，直接计算点积即为余弦相似度
        # 余弦相似度范围为[-1, 1]，1表示完全相同，-1表示完全相反，0表示正交
        similarity = np.dot(features1.flatten(), features2.flatten())

        # 确保结果在合理范围内
        similarity = np.clip(similarity, -1.0, 1.0)

        return float(similarity)
    
    def _load_dataset_from_csv(self, dataset_path, annotation_file=None):
        """
        从CSV标注文件加载数据集

        Args:
            dataset_path: 数据集路径
            annotation_file: CSV标注文件路径，如果为None则自动查找

        Returns:
            list: 包含所有图片对信息的列表
        """
        if not os.path.exists(dataset_path):
            raise FileNotFoundError(f"数据集路径不存在: {dataset_path}")

        # 自动查找标注文件
        if annotation_file is None:
            annotation_file = os.path.join(dataset_path, "annotations.csv")

        if not os.path.exists(annotation_file):
            print(f"⚠️  未找到CSV标注文件: {annotation_file}")
            print("将使用传统方式加载数据集（假设所有样本为正样本）")
            return self._load_dataset_legacy(dataset_path)

        print(f"📋 从CSV标注文件加载数据集: {annotation_file}")

        # 读取CSV文件
        dataset_pairs = []
        try:
            with open(annotation_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    celebrity = row['celebrity']
                    lifestyle_photo = row['lifestyle_photo']
                    test_photo = row['test_photo']
                    label = int(row['label'])  # 1为正样本，0为负样本
                    description = row.get('description', '')

                    # 构建完整路径
                    lifestyle_path = os.path.join(dataset_path, celebrity, lifestyle_photo)
                    test_path = os.path.join(dataset_path, celebrity, test_photo)

                    # 检查文件是否存在
                    if os.path.exists(lifestyle_path) and os.path.exists(test_path):
                        dataset_pairs.append({
                            'celebrity': celebrity,
                            'lifestyle_path': lifestyle_path,
                            'test_path': test_path,
                            'lifestyle_photo': lifestyle_photo,
                            'test_photo': test_photo,
                            'ground_truth': bool(label),  # True为同一人，False为不同人
                            'description': description
                        })
                    else:
                        print(f"⚠️  文件不存在，跳过: {lifestyle_path} 或 {test_path}")

        except Exception as e:
            print(f"❌ 读取CSV文件失败: {e}")
            return self._load_dataset_legacy(dataset_path)

        print(f"📊 数据集加载完成:")
        print(f"   总图片对数: {len(dataset_pairs)}")
        positive_samples = sum(1 for pair in dataset_pairs if pair['ground_truth'])
        negative_samples = len(dataset_pairs) - positive_samples
        print(f"   正样本数量: {positive_samples}")
        print(f"   负样本数量: {negative_samples}")

        return dataset_pairs

    def _load_dataset_legacy(self, dataset_path):
        """
        传统方式加载数据集（兼容旧版本）
        假设所有样本都是正样本
        """
        dataset_pairs = []

        # 遍历每个明星文件夹
        for celebrity_name in os.listdir(dataset_path):
            celebrity_path = os.path.join(dataset_path, celebrity_name)

            if not os.path.isdir(celebrity_path):
                continue

            # 获取该明星的所有图片
            images = []
            for img_file in os.listdir(celebrity_path):
                if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
                    img_path = os.path.join(celebrity_path, img_file)
                    images.append((img_file, img_path))

            if len(images) >= 2:  # 至少需要2张图片
                images.sort()  # 确保顺序一致
                lifestyle_photo, lifestyle_path = images[0]  # 第一张作为生活照

                # 其余图片作为测试照片
                for test_photo, test_path in images[1:]:
                    dataset_pairs.append({
                        'celebrity': celebrity_name,
                        'lifestyle_path': lifestyle_path,
                        'test_path': test_path,
                        'lifestyle_photo': lifestyle_photo,
                        'test_photo': test_photo,
                        'ground_truth': True,  # 传统方式假设都是正样本
                        'description': f'{celebrity_name}生活照vs{celebrity_name}剧照'
                    })

        print(f"📊 数据集加载完成（传统模式）:")
        print(f"   总图片对数: {len(dataset_pairs)}")
        print(f"   所有样本均视为正样本")

        return dataset_pairs

    def verify_celebrity_makeup(self, dataset_path, threshold=0.5, save_results=True, annotation_file=None):
        """
        执行明星妆造验证任务

        Args:
            dataset_path: 数据集路径
            threshold: 相似度阈值，用于判断是否为同一人
            save_results: 是否保存详细结果
            annotation_file: CSV标注文件路径

        Returns:
            dict: 验证结果统计
        """
        print(f"\n🎭 开始影视明星妆造验证任务")
        print(f"📁 数据集路径: {dataset_path}")
        print(f"🎯 相似度阈值: {threshold}")
        print("="*60)

        # 加载数据集
        dataset_pairs = self._load_dataset_from_csv(dataset_path, annotation_file)

        if not dataset_pairs:
            print("❌ 数据集为空，请检查数据集路径和格式")
            return None

        # 验证结果统计
        results = {
            'total_pairs': 0,
            'correct_predictions': 0,
            'true_positives': 0,  # 正确识别的正样本
            'true_negatives': 0,  # 正确识别的负样本
            'false_positives': 0, # 错误识别为正样本的负样本
            'false_negatives': 0, # 错误识别为负样本的正样本
            'accuracy': 0.0,
            'precision': 0.0,
            'recall': 0.0,
            'f1_score': 0.0,
            'similarities': [],
            'detailed_results': []
        }

        # 逐个图片对进行验证
        for pair in tqdm(dataset_pairs, desc="处理图片对"):
            celebrity_name = pair['celebrity']
            lifestyle_path = pair['lifestyle_path']
            test_path = pair['test_path']
            ground_truth = pair['ground_truth']

            # 提取生活照特征
            lifestyle_tensor = self._preprocess_image(lifestyle_path)
            if lifestyle_tensor is None:
                print(f"   ⚠️  跳过生活照处理失败: {lifestyle_path}")
                continue

            # 提取测试照片特征
            test_tensor = self._preprocess_image(test_path)
            if test_tensor is None:
                print(f"   ⚠️  跳过测试照片处理失败: {test_path}")
                continue

            lifestyle_features = self._extract_features(lifestyle_tensor)
            test_features = self._extract_features(test_tensor)

            # 计算相似度
            similarity = self._calculate_similarity(lifestyle_features, test_features)

            # 判断预测结果
            prediction = similarity >= threshold
            is_correct = prediction == ground_truth

            # 更新统计信息
            results['total_pairs'] += 1
            if is_correct:
                results['correct_predictions'] += 1

            # 更新混淆矩阵统计
            if ground_truth and prediction:
                results['true_positives'] += 1
            elif not ground_truth and not prediction:
                results['true_negatives'] += 1
            elif not ground_truth and prediction:
                results['false_positives'] += 1
            elif ground_truth and not prediction:
                results['false_negatives'] += 1

            results['similarities'].append(similarity)
            results['detailed_results'].append({
                'celebrity': celebrity_name,
                'lifestyle_photo': pair['lifestyle_photo'],
                'test_photo': pair['test_photo'],
                'similarity': similarity,
                'prediction': prediction,
                'ground_truth': ground_truth,
                'correct': is_correct,
                'description': pair['description']
            })

            # 实时显示结果
            status = "✅" if is_correct else "❌"
            gt_label = "同一人" if ground_truth else "不同人"
            pred_label = "同一人" if prediction else "不同人"
            print(f"   {status} {celebrity_name}: {pair['test_photo']} | 相似度={similarity:.4f} | 真实={gt_label} | 预测={pred_label}")

        # 计算最终指标
        if results['total_pairs'] > 0:
            results['accuracy'] = (results['correct_predictions'] / results['total_pairs']) * 100

            # 计算精确率、召回率和F1分数
            tp = results['true_positives']
            fp = results['false_positives']
            fn = results['false_negatives']

            if tp + fp > 0:
                results['precision'] = (tp / (tp + fp)) * 100
            if tp + fn > 0:
                results['recall'] = (tp / (tp + fn)) * 100
            if results['precision'] + results['recall'] > 0:
                results['f1_score'] = (2 * results['precision'] * results['recall']) / (results['precision'] + results['recall'])

        # 计算相似度统计信息
        similarities = np.array(results['similarities'])
        results['similarity_stats'] = {
            'mean': float(np.mean(similarities)),
            'std': float(np.std(similarities)),
            'min': float(np.min(similarities)),
            'max': float(np.max(similarities)),
            'median': float(np.median(similarities))
        }

        # 打印结果摘要
        self._print_results_summary(results, threshold)

        # 保存详细结果
        if save_results:
            self._save_results(results, dataset_path, threshold)

        return results

    def _print_results_summary(self, results, threshold):
        """打印结果摘要"""
        print(f"\n🎉 验证任务完成!")
        print("="*60)
        print(f"📊 验证结果统计:")
        print(f"   总图片对数量: {results['total_pairs']}")
        print(f"   判断正确数量: {results['correct_predictions']}")
        print(f"   验证准确率: {results['accuracy']:.2f}%")
        print(f"   相似度阈值: {threshold}")

        # 显示混淆矩阵统计
        print(f"\n📋 混淆矩阵统计:")
        print(f"   真正例 (TP): {results['true_positives']} - 正确识别的同一人")
        print(f"   真负例 (TN): {results['true_negatives']} - 正确识别的不同人")
        print(f"   假正例 (FP): {results['false_positives']} - 错误识别为同一人")
        print(f"   假负例 (FN): {results['false_negatives']} - 错误识别为不同人")

        # 显示性能指标
        print(f"\n📈 性能指标:")
        print(f"   准确率 (Accuracy): {results['accuracy']:.2f}%")
        print(f"   精确率 (Precision): {results['precision']:.2f}%")
        print(f"   召回率 (Recall): {results['recall']:.2f}%")
        print(f"   F1分数 (F1-Score): {results['f1_score']:.2f}")

        print(f"\n📊 特征相似度统计:")
        stats = results['similarity_stats']
        print(f"   平均相似度: {stats['mean']:.4f}")
        print(f"   标准差: {stats['std']:.4f}")
        print(f"   最小值: {stats['min']:.4f}")
        print(f"   最大值: {stats['max']:.4f}")
        print(f"   中位数: {stats['median']:.4f}")

        # 分析错误案例
        incorrect_cases = [r for r in results['detailed_results'] if not r['correct']]
        if incorrect_cases:
            print(f"\n❌ 错误案例分析 ({len(incorrect_cases)}个):")
            for case in incorrect_cases[:5]:  # 只显示前5个
                gt_label = "同一人" if case['ground_truth'] else "不同人"
                pred_label = "同一人" if case['prediction'] else "不同人"
                print(f"   {case['celebrity']}: {case['test_photo']} | 相似度={case['similarity']:.4f} | 真实={gt_label} | 预测={pred_label}")
            if len(incorrect_cases) > 5:
                print(f"   ... 还有 {len(incorrect_cases) - 5} 个错误案例")

    def _save_results(self, results, dataset_path, threshold):
        """保存详细结果到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = "verification_results"
        os.makedirs(output_dir, exist_ok=True)

        # 保存详细结果到CSV
        csv_path = os.path.join(output_dir, f"celebrity_verification_{timestamp}.csv")

        with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['celebrity', 'lifestyle_photo', 'test_photo', 'similarity',
                         'prediction', 'ground_truth', 'correct', 'description']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(results['detailed_results'])

        # 保存统计摘要
        summary_path = os.path.join(output_dir, f"verification_summary_{timestamp}.txt")
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write(f"影视明星妆造验证结果摘要\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据集路径: {dataset_path}\n")
            f.write(f"相似度阈值: {threshold}\n")
            f.write(f"="*50 + "\n")
            f.write(f"总图片对数量: {results['total_pairs']}\n")
            f.write(f"判断正确数量: {results['correct_predictions']}\n")
            f.write(f"验证准确率: {results['accuracy']:.2f}%\n")
            f.write(f"\n混淆矩阵统计:\n")
            f.write(f"真正例 (TP): {results['true_positives']}\n")
            f.write(f"真负例 (TN): {results['true_negatives']}\n")
            f.write(f"假正例 (FP): {results['false_positives']}\n")
            f.write(f"假负例 (FN): {results['false_negatives']}\n")
            f.write(f"\n性能指标:\n")
            f.write(f"准确率 (Accuracy): {results['accuracy']:.2f}%\n")
            f.write(f"精确率 (Precision): {results['precision']:.2f}%\n")
            f.write(f"召回率 (Recall): {results['recall']:.2f}%\n")
            f.write(f"F1分数 (F1-Score): {results['f1_score']:.2f}\n")
            f.write(f"\n特征相似度统计:\n")
            stats = results['similarity_stats']
            f.write(f"平均相似度: {stats['mean']:.4f}\n")
            f.write(f"标准差: {stats['std']:.4f}\n")
            f.write(f"最小值: {stats['min']:.4f}\n")
            f.write(f"最大值: {stats['max']:.4f}\n")
            f.write(f"中位数: {stats['median']:.4f}\n")

        print(f"\n💾 结果已保存:")
        print(f"   详细结果: {csv_path}")
        print(f"   统计摘要: {summary_path}")

    def plot_similarity_distribution(self, results, save_path=None):
        """绘制相似度分布图"""
        try:
            import matplotlib.pyplot as plt
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            similarities = results['similarities']

            plt.figure(figsize=(12, 8))

            # 子图1: 相似度直方图
            plt.subplot(2, 2, 1)
            plt.hist(similarities, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
            plt.xlabel('特征相似度')
            plt.ylabel('频次')
            plt.title('特征相似度分布直方图')
            plt.grid(True, alpha=0.3)

            # 子图2: 相似度箱线图
            plt.subplot(2, 2, 2)
            plt.boxplot(similarities)
            plt.ylabel('特征相似度')
            plt.title('特征相似度箱线图')
            plt.grid(True, alpha=0.3)

            # 子图3: 累积分布
            plt.subplot(2, 2, 3)
            sorted_sim = np.sort(similarities)
            cumulative = np.arange(1, len(sorted_sim) + 1) / len(sorted_sim)
            plt.plot(sorted_sim, cumulative, linewidth=2)
            plt.xlabel('特征相似度')
            plt.ylabel('累积概率')
            plt.title('特征相似度累积分布')
            plt.grid(True, alpha=0.3)

            # 子图4: 统计信息
            plt.subplot(2, 2, 4)
            plt.axis('off')
            stats = results['similarity_stats']
            stats_text = f"""统计信息:

总图片对数: {results['total_pairs']}
验证准确率: {results['accuracy']:.2f}%

相似度统计:
平均值: {stats['mean']:.4f}
标准差: {stats['std']:.4f}
最小值: {stats['min']:.4f}
最大值: {stats['max']:.4f}
中位数: {stats['median']:.4f}"""

            plt.text(0.1, 0.9, stats_text, transform=plt.gca().transAxes,
                    fontsize=12, verticalalignment='top', fontfamily='monospace')

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"📊 相似度分布图已保存: {save_path}")

            plt.show()

        except Exception as e:
            print(f"⚠️  绘图失败: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="影视明星不同妆造形象人脸验证脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python celebrity_makeup_verification.py --dataset data/makeup_dataset --model model/best/068.ckpt
  python celebrity_makeup_verification.py --dataset data/makeup_dataset --model model/SmartFineTune_*/best.ckpt --threshold 0.6
  python celebrity_makeup_verification.py --dataset data/makeup_dataset --model model/best/068.ckpt --plot
        """
    )

    parser.add_argument('--dataset', type=str, default='data/makeup_dataset',
                       help='数据集路径 (默认: data/makeup_dataset)')
    parser.add_argument('--model', type=str,
                       default='model/best/068.ckpt',
                       help='预训练模型路径')
    parser.add_argument('--annotations', type=str, default=None,
                       help='CSV标注文件路径 (默认: 自动查找dataset/annotations.csv)')
    parser.add_argument('--threshold', type=float, default=0.5,
                       help='相似度阈值，用于判断是否为同一人 (默认: 0.5)')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'], help='计算设备 (默认: cuda)')
    parser.add_argument('--plot', action='store_true',
                       help='是否绘制相似度分布图')
    parser.add_argument('--no_save', action='store_true',
                       help='不保存详细结果文件')

    args = parser.parse_args()

    # 检查数据集路径
    if not os.path.exists(args.dataset):
        print(f"❌ 数据集路径不存在: {args.dataset}")
        sys.exit(1)

    # 自动查找模型文件
    model_path = args.model
    if not os.path.exists(model_path):
        # 尝试查找最新的SmartFineTune模型
        model_dir = "model"
        if os.path.exists(model_dir):
            smart_dirs = [d for d in os.listdir(model_dir) if d.startswith('SmartFineTune_')]
            if smart_dirs:
                latest_dir = sorted(smart_dirs)[-1]
                potential_path = os.path.join(model_dir, latest_dir, 'best.ckpt')
                if os.path.exists(potential_path):
                    model_path = potential_path
                    print(f"🔍 自动找到模型: {model_path}")

    if not os.path.exists(model_path):
        print(f"⚠️  模型文件不存在: {model_path}")
        print("将使用随机初始化的模型进行测试")

    try:
        # 创建验证器
        verifier = CelebrityMakeupVerifier(model_path, args.device)

        # 执行验证
        results = verifier.verify_celebrity_makeup(
            args.dataset,
            threshold=args.threshold,
            save_results=not args.no_save,
            annotation_file=args.annotations
        )

        if results is None:
            sys.exit(1)

        # 绘制分布图
        if args.plot:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_path = f"verification_results/similarity_distribution_{timestamp}.png"
            os.makedirs("verification_results", exist_ok=True)
            verifier.plot_similarity_distribution(results, plot_path)

        print(f"\n🎉 任务完成!")

    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
