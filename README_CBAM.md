# MobileFaceNet-CBAM

基于MobileFaceNet实现的人脸识别模型，添加了CBAM注意力机制模块。

## CBAM模块简介

CBAM (Convolutional Block Attention Module) 是一种注意力机制模块，由通道注意力和空间注意力两部分组成：
1. 通道注意力：关注"特征通道应该关注什么"
2. 空间注意力：关注"特征图上的哪些位置需要被关注"

通过顺序结合这两种注意力机制，CBAM可以有效提升模型的表示能力，提高识别精度。

## 模型修改

我们在原始MobileFaceNet模型的关键位置添加了三个CBAM模块：

| 输入 | 操作 | t | c | n | s |
|------|------|---|---|---|---|
| 112×96 × 3 | 3×3卷积 | - | 64 | 1 | 2 |
| 56×48 × 64 | 3×3深度卷积 | - | 64 | 1 | 1 |
| 56×48 × 64 | 瓶颈层 | 2 | 64 | 5 | 2 |
| 28×24 × 64 | 瓶颈层 | 4 | 128 | 1 | 2 |
| 14×12 × 128 | cbam | - | 128 | 1 | 1 |
| 14×12 × 128 | 瓶颈层 | 2 | 128 | 6 | 1 |
| 14×12 × 128 | cbam | - | 128 | 1 | 1 |
| 14×12 × 128 | 瓶颈层 | 4 | 128 | 1 | 2 |
| 7×6 × 128 | cbam | - | 128 | 1 | 1 |
| 7×6 × 128 | 瓶颈层 | 2 | 128 | 2 | 1 |
| 7×6 × 128 | 1×1卷积 | - | 512 | 1 | 1 |
| 7×6 × 512 | 线性7×6全局深度卷积 | - | 512 | 1 | 1 |
| 1×1 × 512 | 线性1×1卷积 | - | 128 | 1 | 1 |

## 微调模型

我们提供了一个简单的微调脚本，用于从预训练模型开始微调带有CBAM模块的模型。

### 参数冻结策略

为了保留预训练模型中学习到的底层特征，我们采用了参数冻结策略：

1. 冻结初始的3×3卷积层参数
2. 冻结3×3深度卷积层参数
3. 冻结第一个瓶颈层（前5个blocks）参数

这种策略可以保留模型对基本特征的提取能力，同时允许更高层次的特征表示和CBAM注意力模块进行优化，从而提高模型性能。

### 运行微调

1. 确保已经有预训练的MobileFaceNet模型（默认位置：`./model/best/068.ckpt`）
2. 运行微调脚本：

```bash
# 赋予执行权限
chmod +x run_finetune.sh

# 运行微调脚本
./run_finetune.sh
```

或者直接运行Python脚本：

```bash
python finetune.py --pretrained ./model/best/068.ckpt --epochs 20 --lr 0.0001 --batch-size 128
```

### 参数说明

- `--pretrained`: 预训练模型路径
- `--epochs`: 微调的总轮数
- `--lr`: 初始学习率
- `--model-prefix`: 模型保存前缀
- `--batch-size`: 批量大小

## 注意事项

1. CBAM模块的参数会随机初始化，其他部分的参数会从预训练模型加载
2. 为了加速CBAM模块的收敛，我们给CBAM模块使用了更高的学习率
3. 微调完成后，最佳模型会保存在`./model/MobileFaceNet_CBAM_*`目录下
4. 本模型使用112×96的输入图像尺寸，与原始LFW数据集保持一致
5. 初始层参数被冻结，只有后续层和CBAM模块参与训练

## 参考

- [CBAM: Convolutional Block Attention Module](https://arxiv.org/abs/1807.06521)
- [MobileFaceNet: An Efficient CNN for Face Recognition](https://arxiv.org/abs/1804.07573) 