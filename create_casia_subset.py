import os
import torch
import numpy as np
import argparse
import json
from dataloader.CASIA_Face_subset import CASIA_Face_Subset
from config import CASIA_DATA_DIR

def parse_args():
    parser = argparse.ArgumentParser(description='CASIA-WebFace子集生成器')
    parser.add_argument('--root', type=str, default=CASIA_DATA_DIR,
                        help='CASIA-WebFace数据集根目录')
    parser.add_argument('--num-identities', type=int, default=300,
                        help='要选择的身份数量')
    parser.add_argument('--min-images', type=int, default=20,
                        help='每个身份至少包含的图片数量')
    parser.add_argument('--seed', type=int, default=42,
                        help='随机种子，用于可重复性')
    parser.add_argument('--save-info', action='store_true',
                        help='是否保存子集信息到JSON文件')
    return parser.parse_args()

def main():
    args = parse_args()
    
    print("=" * 50)
    print(f"创建CASIA-WebFace子集")
    print(f"数据集路径: {args.root}")
    print(f"目标身份数量: {args.num_identities}")
    print(f"每个身份最少图片: {args.min_images}")
    print(f"随机种子: {args.seed}")
    print("=" * 50)
    
    # 创建子集
    dataset = CASIA_Face_Subset(
        root=args.root,
        num_identities=args.num_identities,
        min_images_per_identity=args.min_images,
        seed=args.seed
    )
    
    # 获取统计信息
    stats = dataset.get_identity_stats()
    min_images = min(stats.values())
    max_images = max(stats.values())
    avg_images = sum(stats.values()) / len(stats)
    
    print("\n子集统计信息:")
    print(f"身份数量: {dataset.class_nums}")
    print(f"总图片数量: {len(dataset)}")
    print(f"每个身份的图片数量 - 最少: {min_images}, 最多: {max_images}, 平均: {avg_images:.2f}")
    
    # 测试数据加载
    trainloader = torch.utils.data.DataLoader(dataset, batch_size=32, shuffle=True, num_workers=4, drop_last=False)
    for i, data in enumerate(trainloader):
        if i == 0:
            print(f"批次数据形状: {data[0].shape}")
            break
    
    # 保存子集信息
    if args.save_info:
        subset_info = {
            'num_identities': dataset.class_nums,
            'total_images': len(dataset),
            'min_images_per_identity': min_images,
            'max_images_per_identity': max_images,
            'avg_images_per_identity': float(f"{avg_images:.2f}"),
            'selected_identities': list(dataset.selected_identities.keys()),
            'identity_mapping': dataset.selected_identities,
            'stats': stats
        }
        
        # 创建输出目录
        os.makedirs('output', exist_ok=True)
        output_file = f'output/casia_subset_info_{args.num_identities}ids_{args.min_images}min_{args.seed}.json'
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(subset_info, f, indent=4)
        
        print(f"\n子集信息已保存到: {output_file}")
        
        # 保存子集图片和标签列表（可用于重现）
        image_list_file = f'output/casia_subset_images_{args.num_identities}ids_{args.min_images}min_{args.seed}.txt'
        with open(image_list_file, 'w', encoding='utf-8') as f:
            for img_path, label in zip(dataset.image_list, dataset.label_list):
                rel_path = os.path.relpath(img_path, start=args.root)
                f.write(f"{rel_path} {label}\n")
        
        print(f"子集图片列表已保存到: {image_list_file}")

if __name__ == '__main__':
    main() 