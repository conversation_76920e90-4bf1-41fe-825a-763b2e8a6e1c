#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
注意力可视化方法测试脚本
对比Hook-based CBAM和Grad-CAM两种方法的效果
"""

import os
import sys
import time
import argparse
from pathlib import Path

def test_hook_based_method(image_path, model_path, output_dir):
    """测试Hook-based CBAM方法"""
    print("\n" + "="*50)
    print("🔍 测试 Hook-based CBAM 注意力可视化")
    print("="*50)
    
    try:
        from attention_heatmap_visualizer import AttentionVisualizer
        
        start_time = time.time()
        
        # 初始化可视化器
        visualizer = AttentionVisualizer(model_path)
        
        # 创建输出目录
        hook_output_dir = os.path.join(output_dir, "hook_based")
        
        # 执行可视化
        visualizer.visualize_all_stages(
            image_path, 
            hook_output_dir,
            save_individual=True,
            save_combined=True
        )
        
        end_time = time.time()
        
        print(f"✅ Hook-based方法完成")
        print(f"⏱️  耗时: {end_time - start_time:.2f}秒")
        print(f"📁 输出目录: {hook_output_dir}")
        
        return True, end_time - start_time
        
    except Exception as e:
        print(f"❌ Hook-based方法失败: {e}")
        return False, 0

def test_gradcam_method(image_path, model_path, output_dir):
    """测试Grad-CAM方法"""
    print("\n" + "="*50)
    print("🔍 测试 Grad-CAM 注意力可视化")
    print("="*50)
    
    try:
        from gradcam_visualizer import GradCAMVisualizer
        
        start_time = time.time()
        
        # 初始化可视化器
        visualizer = GradCAMVisualizer(model_path)
        
        # 创建输出目录
        gradcam_output_dir = os.path.join(output_dir, "gradcam")
        
        # 执行可视化
        visualizer.visualize_all_layers(
            image_path,
            gradcam_output_dir
        )
        
        end_time = time.time()
        
        print(f"✅ Grad-CAM方法完成")
        print(f"⏱️  耗时: {end_time - start_time:.2f}秒")
        print(f"📁 输出目录: {gradcam_output_dir}")
        
        return True, end_time - start_time
        
    except Exception as e:
        print(f"❌ Grad-CAM方法失败: {e}")
        return False, 0

def compare_results(output_dir):
    """比较两种方法的结果"""
    print("\n" + "="*50)
    print("📊 结果对比分析")
    print("="*50)
    
    hook_dir = os.path.join(output_dir, "hook_based")
    gradcam_dir = os.path.join(output_dir, "gradcam")
    
    # 统计文件数量
    hook_files = []
    gradcam_files = []
    
    if os.path.exists(hook_dir):
        hook_files = [f for f in os.listdir(hook_dir) if f.endswith('.png')]
    
    if os.path.exists(gradcam_dir):
        gradcam_files = [f for f in os.listdir(gradcam_dir) if f.endswith('.png')]
    
    print(f"📈 Hook-based方法生成图像: {len(hook_files)}张")
    print(f"📈 Grad-CAM方法生成图像: {len(gradcam_files)}张")
    
    # 显示文件列表
    if hook_files:
        print(f"\n🖼️  Hook-based生成的文件:")
        for f in sorted(hook_files):
            print(f"   - {f}")
    
    if gradcam_files:
        print(f"\n🖼️  Grad-CAM生成的文件:")
        for f in sorted(gradcam_files):
            print(f"   - {f}")

def main():
    """主程序"""
    parser = argparse.ArgumentParser(
        description='注意力可视化方法对比测试',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 对比两种方法
  python test_attention_methods.py --image test.jpg --model model.pth --output ./comparison

  # 只测试Hook-based方法
  python test_attention_methods.py --image test.jpg --model model.pth --output ./test --hook_only

  # 只测试Grad-CAM方法
  python test_attention_methods.py --image test.jpg --model model.pth --output ./test --gradcam_only
        """
    )
    
    parser.add_argument('--image', type=str, required=True,
                       help='测试图像路径')
    parser.add_argument('--model', type=str, 
                       default='model/best/mobilefacenet_cbam.pth',
                       help='模型权重文件路径')
    parser.add_argument('--output', type=str, required=True,
                       help='输出目录路径')
    parser.add_argument('--hook_only', action='store_true',
                       help='只测试Hook-based方法')
    parser.add_argument('--gradcam_only', action='store_true',
                       help='只测试Grad-CAM方法')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'], help='计算设备')
    
    args = parser.parse_args()
    
    # 参数验证
    if not os.path.exists(args.image):
        print(f"❌ 图像文件不存在: {args.image}")
        sys.exit(1)
    
    if not os.path.exists(args.model):
        print(f"⚠️  模型文件不存在: {args.model}")
        print("将使用未训练的模型进行测试")
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 打印测试信息
    print("🚀 注意力可视化方法对比测试")
    print("="*60)
    print(f"📋 测试配置:")
    print(f"   测试图像: {args.image}")
    print(f"   模型路径: {args.model}")
    print(f"   输出目录: {args.output}")
    print(f"   计算设备: {args.device}")
    print(f"   测试模式: ", end="")
    
    if args.hook_only:
        print("仅Hook-based方法")
    elif args.gradcam_only:
        print("仅Grad-CAM方法")
    else:
        print("两种方法对比")
    
    print("="*60)
    
    # 执行测试
    results = {}
    
    if not args.gradcam_only:
        success, time_cost = test_hook_based_method(args.image, args.model, args.output)
        results['hook_based'] = {'success': success, 'time': time_cost}
    
    if not args.hook_only:
        success, time_cost = test_gradcam_method(args.image, args.model, args.output)
        results['gradcam'] = {'success': success, 'time': time_cost}
    
    # 显示结果对比
    if not args.hook_only and not args.gradcam_only:
        compare_results(args.output)
    
    # 性能总结
    print("\n" + "="*50)
    print("📊 性能总结")
    print("="*50)
    
    for method, result in results.items():
        status = "✅ 成功" if result['success'] else "❌ 失败"
        time_info = f"({result['time']:.2f}秒)" if result['success'] else ""
        print(f"{method.replace('_', '-').title()}: {status} {time_info}")
    
    # 推荐建议
    print(f"\n💡 使用建议:")
    print(f"   - Hook-based方法：适合分析CBAM注意力机制的内部工作原理")
    print(f"   - Grad-CAM方法：适合解释模型对特定类别的决策依据")
    print(f"   - 建议结合使用两种方法获得更全面的理解")
    
    print(f"\n🎉 测试完成！请查看输出目录: {args.output}")

if __name__ == "__main__":
    main()
