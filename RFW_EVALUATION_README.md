# RFW数据集评估脚本使用说明

## 概述

本脚本用于在RFW (Racial Faces in the Wild) 数据集上评估预训练的人脸识别模型。RFW数据集包含四个种族的人脸图像：
- **African** (非洲人)
- **Asian** (亚洲人) 
- **Caucasian** (高加索人)
- **Indian** (印度人)

脚本会对每个种族分别计算准确率、AUC等指标，并生成可视化结果。

## 文件说明

- `rfw_evaluation.py` - 主要的评估脚本，包含完整的评估逻辑，支持基准模型和CBAM模型
- `run_rfw_evaluation.py` - 简化的运行脚本，提供交互式选择界面
- `compare_baseline_cbam.py` - 专门用于比较基准模型和CBAM模型性能的脚本
- `RFW_EVALUATION_README.md` - 本说明文件

## 数据集结构要求

确保RFW数据集按以下结构放置：

```
data/RFW/
├── data/
│   ├── African/
│   │   ├── m.010lz5/
│   │   │   ├── m.010lz5_0001.jpg
│   │   │   ├── m.010lz5_0002.jpg
│   │   │   └── ...
│   │   └── ...
│   ├── Asian/
│   ├── Caucasian/
│   └── Indian/
└── txts/
    ├── African/
    │   └── African_pairs.txt
    ├── Asian/
    │   └── Asian_pairs.txt
    ├── Caucasian/
    │   └── Caucasian_pairs.txt
    └── Indian/
        └── Indian_pairs.txt
```

## 使用方法

### 方法1：使用简化脚本（推荐）

```bash
python run_rfw_evaluation.py
```

这个脚本提供交互式选择界面：
1. 评估单个模型（自动检测）
2. 评估基准模型（MobileFaceNet）
3. 评估CBAM模型（MobileFaceNet+CBAM）
4. 比较基准模型和CBAM模型

### 方法2：专门的模型比较脚本

```bash
python compare_baseline_cbam.py
```

这个脚本专门用于比较基准模型和CBAM模型的性能，会：
1. 自动查找基准模型和CBAM模型
2. 分别评估两个模型
3. 生成详细的比较报告

### 方法3：使用完整脚本

```bash
# 使用默认参数（自动检测模型类型）
python rfw_evaluation.py

# 指定基准模型
python rfw_evaluation.py --model_path model/mobilefacenet_best/068.ckpt --model_type base

# 指定CBAM模型
python rfw_evaluation.py --model_path model/MobileFaceNet_CBAM_*/best.ckpt --model_type cbam

# 比较两种模型
python rfw_evaluation.py --compare_models

# 使用CPU（如果没有GPU）
python rfw_evaluation.py --device cpu
```

### 参数说明

- `--model_path`: 预训练模型路径（.ckpt文件）
- `--model_type`: 模型类型，可选值：
  - `auto`: 自动检测（默认）
  - `base`: 基准MobileFaceNet模型
  - `cbam`: MobileFaceNet+CBAM模型
- `--rfw_data_dir`: RFW数据集根目录（默认：./data/RFW）
- `--device`: 计算设备，cuda或cpu（默认：cuda）
- `--output_dir`: 结果输出目录（默认：./rfw_results）
- `--compare_models`: 比较基准模型和CBAM模型

## 输出结果

脚本运行完成后，会在输出目录生成以下文件：

1. **rfw_roc_curves.png** - ROC曲线图
   - 显示四个种族的ROC曲线和AUC值
   - 用于比较不同种族的模型性能

2. **rfw_accuracy_comparison.png** - 准确率对比图
   - 柱状图显示各种族的准确率和AUC
   - 直观比较种族间的性能差异

3. **rfw_detailed_results.csv** - 详细结果CSV文件
   - 包含每个种族的详细评估指标
   - 可用于进一步分析

4. **控制台报告** - 实时显示的评估报告
   - 总体统计信息
   - 各种族详细结果
   - 性能差异分析

## 评估指标说明

- **准确率 (Accuracy)**: 正确分类的样本比例
- **AUC**: ROC曲线下面积，衡量分类器性能
- **精确率 (Precision)**: 预测为正样本中实际为正样本的比例
- **召回率 (Recall)**: 实际正样本中被正确预测的比例
- **F1分数**: 精确率和召回率的调和平均数
- **最佳阈值**: 使TPR-FPR最大化的相似度阈值

## 示例输出

```
🎯 RFW数据集评估报告
================================================================================

📊 总体统计:
   处理的图像对总数: 24,000
   平均准确率: 92.45%
   平均AUC: 95.23%

📈 各种族详细结果:
--------------------------------------------------------------------------------
种族          准确率      AUC        精确率      召回率      F1分数    
--------------------------------------------------------------------------------
African      91.23      94.56      90.45      92.10      91.27     
Asian        93.67      95.89      94.12      93.22      93.67     
Caucasian    94.12      96.45      95.23      92.89      94.05     
Indian       90.78      94.02      89.67      91.89      90.77     

🔍 性能差异分析:
   最高准确率: 94.12%
   最低准确率: 90.78%
   准确率差异: 3.34%
   准确率标准差: 1.45%
   ✅ 不同种族间性能相对均衡
================================================================================
```

## 依赖要求

确保安装了以下Python包：
- torch
- torchvision
- opencv-python
- numpy
- matplotlib
- scikit-learn
- pandas
- tqdm
- pathlib

## 注意事项

1. **GPU内存**: 如果GPU内存不足，脚本会自动切换到CPU
2. **数据集完整性**: 确保所有pairs.txt文件中引用的图像都存在
3. **模型兼容性**: 脚本适用于MobileFaceNet+CBAM架构的模型
4. **处理时间**: 完整评估可能需要较长时间，取决于数据集大小和硬件性能

## 故障排除

### 常见问题

1. **找不到模型文件**
   ```
   ❌ 未找到预训练模型
   ```
   解决：确保model目录下存在.ckpt文件，或使用--model_path指定路径

2. **找不到数据集**
   ```
   ❌ RFW数据集不存在: ./data/RFW
   ```
   解决：确保RFW数据集正确放置在data/RFW目录下

3. **CUDA内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   解决：使用--device cpu参数切换到CPU模式

4. **图像读取失败**
   ```
   ⚠️ 处理图像对时出错: 无法读取图像
   ```
   解决：检查图像文件是否损坏或路径是否正确


