# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.ckpt
*.pt

# 数据集
data/
dataset/
datasets/

# 模型文件
model/
models/
checkpoints/
*.model

# 结果文件
result/
results/
output/
outputs/
logs/
*.log

# 环境配置
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# 测试
.coverage
htmlcov/
.tox/
.pytest_cache/

# 系统文件
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp
*.bak
*.backup

# 可视化结果
*.png
*.jpg
*.jpeg
*.gif
*.svg
!docs/images/*

# 配置文件备份
config_backup.py
*.conf.bak

# 数据文件
*.mat
*.txt
!requirements.txt
!README*.txt
!*.md

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z
