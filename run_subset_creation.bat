@echo off
chcp 65001 > nul
echo ======================================================
echo CASIA-WebFace子集生成器
echo ======================================================

REM 设置Python路径 - 如需要修改为你的Python路径
set PYTHON=python

REM 默认参数
set NUM_IDENTITIES=500
set MIN_IMAGES=20
set SEED=42
set SAVE_INFO=--save-info

echo 正在生成CASIA-WebFace子集...
echo 身份数量: %NUM_IDENTITIES%
echo 每个身份最少图片: %MIN_IMAGES%
echo 随机种子: %SEED%

REM 确保输出目录存在
if not exist output mkdir output

echo 运行Python脚本...
%PYTHON% create_casia_subset.py --num-identities %NUM_IDENTITIES% --min-images %MIN_IMAGES% --seed %SEED% %SAVE_INFO%

if %ERRORLEVEL% NEQ 0 (
    echo 错误：Python脚本执行失败，错误代码：%ERRORLEVEL%
    goto end
)

echo.
if exist output (
    echo 检查输出目录内容:
    dir output
) else (
    echo 警告：输出目录不存在！
)

echo.
echo 子集生成完成！结果应保存在output目录中

:end
pause 