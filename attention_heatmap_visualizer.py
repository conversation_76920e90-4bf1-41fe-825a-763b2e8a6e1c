#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MobileFaceNet + CBAM 注意力热力图可视化工具
支持多阶段热力图生成，中文字体显示，高质量渲染
"""

import os
os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'
import sys
import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from PIL import Image, ImageFont
import cv2
from torchvision import transforms
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')

# 导入模型
from core.model import MobileFacenet
from core.cbam import CBAM, ChannelAttention, SpatialAttention


class ChineseFontManager:
    """中文字体管理器"""
    
    def __init__(self):
        self.font_paths = [
            'C:/Windows/Fonts/msyh.ttc',      # 微软雅黑
            'C:/Windows/Fonts/simhei.ttf',    # 黑体
            'C:/Windows/Fonts/simsun.ttc',    # 宋体
            'C:/Windows/Fonts/kaiti.ttf',     # 楷体
            '/System/Library/Fonts/PingFang.ttc',  # macOS
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
        ]
        self.setup_matplotlib_font()
    
    def setup_matplotlib_font(self):
        """设置matplotlib中文字体"""
        font_found = False
        
        for font_path in self.font_paths:
            if os.path.exists(font_path):
                try:
                    font_prop = fm.FontProperties(fname=font_path)
                    plt.rcParams['font.family'] = font_prop.get_name()
                    plt.rcParams['axes.unicode_minus'] = False
                    font_found = True
                    print(f"✓ 成功设置中文字体: {font_path}")
                    break
                except Exception as e:
                    continue
        
        if not font_found:
            # 备用方案
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            print("⚠ 使用备用中文字体设置")


class AttentionHook:
    """注意力钩子类，用于捕获模型中间层的输出"""
    
    def __init__(self, name: str):
        self.name = name
        self.features = []
        self.channel_attention = []
        self.spatial_attention = []
    
    def clear(self):
        """清空保存的特征"""
        self.features.clear()
        self.channel_attention.clear()
        self.spatial_attention.clear()
    
    def hook_fn(self, module, input, output):
        """钩子函数"""
        self.features.append(output.detach().cpu())
    
    def channel_hook_fn(self, module, input, output):
        """通道注意力钩子函数"""
        self.channel_attention.append(output.detach().cpu())
    
    def spatial_hook_fn(self, module, input, output):
        """空间注意力钩子函数"""
        self.spatial_attention.append(output.detach().cpu())


class AttentionVisualizer:
    """注意力可视化器"""
    
    def __init__(self, model_path: str, device: str = 'cuda'):
        """
        初始化可视化器
        
        Args:
            model_path: 模型权重文件路径
            device: 计算设备
        """
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.font_manager = ChineseFontManager()
        
        # 加载模型
        self.model = self._load_model(model_path)
        self.hooks = {}
        self.hook_handles = []
        
        # 图像预处理
        self.transform = transforms.Compose([
            transforms.Resize((112, 96)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])
        
        print(f"✓ 注意力可视化器初始化完成，使用设备: {self.device}")
    
    def _load_model(self, model_path: str) -> nn.Module:
        """加载模型"""
        model = MobileFacenet()
        
        if os.path.exists(model_path):
            try:
                checkpoint = torch.load(model_path, map_location=self.device)
                if 'net_state_dict' in checkpoint:
                    model.load_state_dict(checkpoint['net_state_dict'])
                else:
                    model.load_state_dict(checkpoint)
                print(f"✓ 成功加载模型: {model_path}")
            except Exception as e:
                print(f"⚠ 模型加载失败: {e}")
                print("使用未训练的模型进行可视化")
        else:
            print(f"⚠ 模型文件不存在: {model_path}")
            print("使用未训练的模型进行可视化")
        
        model.to(self.device)
        model.eval()
        return model
    
    def register_hooks(self):
        """注册钩子到CBAM模块"""
        self.clear_hooks()
        
        cbam_count = 0
        for name, module in self.model.named_modules():
            if isinstance(module, CBAM):
                cbam_count += 1
                hook_name = f"cbam_{cbam_count}"
                
                # 创建钩子对象
                hook = AttentionHook(hook_name)
                self.hooks[hook_name] = hook
                
                # 注册钩子到CBAM的各个子模块
                ca_handle = module.ca.register_forward_hook(hook.channel_hook_fn)
                sa_handle = module.sa.register_forward_hook(hook.spatial_hook_fn)
                cbam_handle = module.register_forward_hook(hook.hook_fn)
                
                self.hook_handles.extend([ca_handle, sa_handle, cbam_handle])
                
                print(f"✓ 注册钩子到 {name} -> {hook_name}")
        
        print(f"✓ 总共注册了 {cbam_count} 个CBAM模块的钩子")
    
    def clear_hooks(self):
        """清除所有钩子"""
        for handle in self.hook_handles:
            handle.remove()
        self.hook_handles.clear()
        self.hooks.clear()
    
    def preprocess_image(self, image_path: str) -> Tuple[torch.Tensor, np.ndarray]:
        """
        预处理图像
        
        Args:
            image_path: 图像路径
            
        Returns:
            处理后的tensor和原始图像数组
        """
        # 加载原始图像
        image = Image.open(image_path).convert('RGB')
        original_image = np.array(image)
        
        # 预处理用于模型输入
        image_tensor = self.transform(image).unsqueeze(0)
        
        return image_tensor, original_image
    
    def extract_features(self, image_tensor: torch.Tensor) -> Dict:
        """
        提取注意力特征
        
        Args:
            image_tensor: 输入图像tensor
            
        Returns:
            提取的特征字典
        """
        # 清空之前的特征
        for hook in self.hooks.values():
            hook.clear()
        
        # 前向传播
        with torch.no_grad():
            _ = self.model(image_tensor.to(self.device))
        
        # 收集特征
        features = {}
        for name, hook in self.hooks.items():
            features[name] = {
                'channel_attention': hook.channel_attention[0] if hook.channel_attention else None,
                'spatial_attention': hook.spatial_attention[0] if hook.spatial_attention else None,
                'output': hook.features[0] if hook.features else None
            }
        
        return features

    def generate_heatmap(self, attention_map: np.ndarray, original_shape: Tuple[int, int],
                        colormap: str = 'jet') -> np.ndarray:
        """
        生成热力图

        Args:
            attention_map: 注意力图
            original_shape: 原始图像形状 (height, width)
            colormap: 颜色映射

        Returns:
            热力图数组
        """
        # 调整大小到原始图像尺寸
        if len(attention_map.shape) == 4:  # (1, 1, H, W)
            attention_map = attention_map[0, 0]
        elif len(attention_map.shape) == 3:  # (1, H, W)
            attention_map = attention_map[0]

        # 调整大小
        attention_resized = cv2.resize(attention_map, (original_shape[1], original_shape[0]))

        # 归一化到0-1
        attention_norm = (attention_resized - attention_resized.min()) / \
                        (attention_resized.max() - attention_resized.min() + 1e-8)

        # 应用颜色映射
        heatmap = cv2.applyColorMap(np.uint8(255 * attention_norm),
                                   getattr(cv2, f'COLORMAP_{colormap.upper()}'))
        heatmap = cv2.cvtColor(heatmap, cv2.COLOR_BGR2RGB)

        return heatmap, attention_norm

    def create_overlay(self, original_image: np.ndarray, heatmap: np.ndarray,
                      alpha: float = 0.6) -> np.ndarray:
        """
        创建叠加图像

        Args:
            original_image: 原始图像
            heatmap: 热力图
            alpha: 热力图透明度

        Returns:
            叠加后的图像
        """
        overlay = heatmap * alpha + original_image * (1 - alpha)
        return overlay.astype(np.uint8)

    def generate_unified_attention(self, stage_features: Dict, original_shape: Tuple[int, int]) -> np.ndarray:
        """
        生成统一的注意力图，融合通道注意力和空间注意力

        Args:
            stage_features: 阶段特征字典
            original_shape: 原始图像形状 (height, width)

        Returns:
            统一的注意力图
        """
        unified_attention = None

        # 处理通道注意力
        if stage_features['channel_attention'] is not None and stage_features['output'] is not None:
            ca_map = stage_features['channel_attention'].numpy()
            output_features = stage_features['output'].numpy()[0]  # (C, H, W)

            if len(ca_map.shape) == 4:  # (1, C, 1, 1)
                ca_weights = ca_map[0, :, 0, 0]
                # 使用通道权重加权特征图
                weighted_features = output_features * ca_weights.reshape(-1, 1, 1)
                channel_attention = np.mean(weighted_features, axis=0)

                # 调整大小到原始图像尺寸
                channel_attention = cv2.resize(channel_attention, (original_shape[1], original_shape[0]))
                unified_attention = channel_attention

        # 处理空间注意力
        if stage_features['spatial_attention'] is not None:
            sa_map = stage_features['spatial_attention'].numpy()

            # 调整空间注意力图的维度
            if len(sa_map.shape) == 4:  # (1, 1, H, W)
                spatial_attention = sa_map[0, 0]
            elif len(sa_map.shape) == 3:  # (1, H, W)
                spatial_attention = sa_map[0]
            else:
                spatial_attention = sa_map

            # 调整大小到原始图像尺寸
            spatial_attention = cv2.resize(spatial_attention, (original_shape[1], original_shape[0]))

            # 融合通道注意力和空间注意力
            if unified_attention is not None:
                # 归一化两个注意力图
                unified_attention = (unified_attention - unified_attention.min()) / \
                                  (unified_attention.max() - unified_attention.min() + 1e-8)
                spatial_attention = (spatial_attention - spatial_attention.min()) / \
                                  (spatial_attention.max() - spatial_attention.min() + 1e-8)

                # 使用加权平均融合（空间注意力权重更高）
                unified_attention = 0.3 * unified_attention + 0.7 * spatial_attention
            else:
                unified_attention = spatial_attention

        # 如果都没有，尝试使用输出特征
        if unified_attention is None and stage_features['output'] is not None:
            output_features = stage_features['output'].numpy()[0]  # (C, H, W)
            # 简单地对所有通道求平均
            unified_attention = np.mean(output_features, axis=0)
            unified_attention = cv2.resize(unified_attention, (original_shape[1], original_shape[0]))

        # 最终归一化
        if unified_attention is not None:
            unified_attention = (unified_attention - unified_attention.min()) / \
                              (unified_attention.max() - unified_attention.min() + 1e-8)

        return unified_attention

    def visualize_single_stage(self, features: Dict, stage_name: str, original_image: np.ndarray,
                              output_dir: str, base_filename: str, save_individual: bool = True):
        """
        可视化单个阶段的注意力

        Args:
            features: 特征字典
            stage_name: 阶段名称
            original_image: 原始图像
            output_dir: 输出目录
            base_filename: 基础文件名
            save_individual: 是否保存单独的图像
        """
        if stage_name not in features:
            return None

        stage_features = features[stage_name]
        original_shape = original_image.shape[:2]

        # 创建子图 - 最优展示：1行2列（原图 + 叠加图）
        fig, axes = plt.subplots(1, 2, figsize=(12, 6))
        fig.suptitle(f'{stage_name.upper()} 注意力可视化', fontsize=16, fontweight='bold')

        # 显示原始图像
        axes[0].imshow(original_image)
        axes[0].set_title('原始图像', fontsize=14, fontweight='bold')
        axes[0].axis('off')

        # 生成统一的注意力图并直接显示叠加结果
        unified_attention = self.generate_unified_attention(stage_features, original_shape)

        if unified_attention is not None:
            # 生成彩色热力图
            heatmap, _ = self.generate_heatmap(unified_attention, original_shape)

            # 生成叠加图像
            overlay = self.create_overlay(original_image, heatmap, alpha=0.5)
            axes[1].imshow(overlay)
            axes[1].set_title('注意力叠加图', fontsize=14, fontweight='bold')
            axes[1].axis('off')
        else:
            # 如果没有注意力信息，显示提示
            axes[1].text(0.5, 0.5, '无注意力信息', ha='center', va='center',
                        transform=axes[1].transAxes, fontsize=12)
            axes[1].set_title('注意力叠加图', fontsize=14, fontweight='bold')
            axes[1].axis('off')

        plt.tight_layout()

        # 保存图像
        if save_individual:
            os.makedirs(output_dir, exist_ok=True)  # 确保输出目录存在
            output_path = os.path.join(output_dir, f"{base_filename}_{stage_name}.png")
            plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"✓ 保存 {stage_name} 可视化: {output_path}")

        return fig

    def visualize_all_stages(self, image_path: str, output_dir: str,
                           save_individual: bool = True, save_combined: bool = True):
        """
        可视化所有阶段的注意力

        Args:
            image_path: 输入图像路径
            output_dir: 输出目录
            save_individual: 是否保存单独的阶段图像
            save_combined: 是否保存组合图像
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 预处理图像
        image_tensor, original_image = self.preprocess_image(image_path)

        # 注册钩子
        self.register_hooks()

        # 提取特征
        print("🔍 提取注意力特征...")
        features = self.extract_features(image_tensor)

        # 获取基础文件名
        base_filename = Path(image_path).stem

        # 可视化每个阶段
        if save_individual:
            for stage_name in sorted(features.keys()):
                print(f"📊 生成 {stage_name} 单独可视化...")
                fig = self.visualize_single_stage(
                    features, stage_name, original_image,
                    output_dir, base_filename, save_individual=True
                )
                if fig:
                    plt.close(fig)

        # 创建最优的组合对比图像
        if save_combined:
            print(f"📊 生成注意力对比图...")
            self.create_combined_visualization(features, original_image, output_dir, base_filename)

        self.clear_hooks()
        print(f"✅ 完成所有可视化，输出目录: {output_dir}")

    def create_combined_visualization(self, features: Dict, original_image: np.ndarray,
                                    output_dir: str, base_filename: str):
        """
        创建最优的组合可视化图像 - 显示原图和所有阶段的注意力叠加图

        Args:
            features: 所有阶段的特征字典
            original_image: 原始图像
            output_dir: 输出目录
            base_filename: 基础文件名
        """
        num_stages = len(features)
        if num_stages == 0:
            return

        # 创建最优布局：1行显示原图 + 所有阶段的叠加图
        fig, axes = plt.subplots(1, num_stages + 1, figsize=(4 * (num_stages + 1), 6))
        fig.suptitle(f'MobileFaceNet + CBAM 注意力可视化对比\n文件: {base_filename}',
                    fontsize=16, fontweight='bold')

        # 确保axes是数组
        if num_stages == 0:
            axes = [axes]
        elif not isinstance(axes, np.ndarray):
            axes = [axes]

        # 显示原始图像
        axes[0].imshow(original_image)
        axes[0].set_title('原始图像', fontsize=14, fontweight='bold')
        axes[0].axis('off')

        # 显示每个阶段的注意力叠加图
        original_shape = original_image.shape[:2]

        for i, stage_name in enumerate(sorted(features.keys()), 1):
            stage_features = features[stage_name]

            # 生成统一注意力图
            unified_attention = self.generate_unified_attention(stage_features, original_shape)

            if unified_attention is not None:
                # 生成热力图和叠加图
                heatmap, _ = self.generate_heatmap(unified_attention, original_shape)
                overlay = self.create_overlay(original_image, heatmap, alpha=0.5)

                axes[i].imshow(overlay)
                axes[i].set_title(f'{stage_name.upper()}\n注意力叠加', fontsize=12, fontweight='bold')
                axes[i].axis('off')
            else:
                axes[i].text(0.5, 0.5, '无注意力\n信息', ha='center', va='center',
                           transform=axes[i].transAxes, fontsize=12)
                axes[i].set_title(f'{stage_name.upper()}\n注意力叠加', fontsize=12, fontweight='bold')
                axes[i].axis('off')

        plt.tight_layout()

        # 保存组合图像
        os.makedirs(output_dir, exist_ok=True)
        combined_path = os.path.join(output_dir, f"{base_filename}_attention_comparison.png")
        plt.savefig(combined_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close(fig)

        print(f"✓ 保存注意力对比图: {combined_path}")

    def batch_visualize(self, input_dir: str, output_dir: str,
                       image_extensions: List[str] = None):
        """
        批量可视化

        Args:
            input_dir: 输入图像目录
            output_dir: 输出目录
            image_extensions: 支持的图像扩展名
        """
        if image_extensions is None:
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

        input_path = Path(input_dir)
        if not input_path.exists():
            print(f"❌ 输入目录不存在: {input_dir}")
            return

        # 查找所有图像文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(input_path.glob(f"*{ext}"))
            image_files.extend(input_path.glob(f"*{ext.upper()}"))

        if not image_files:
            print(f"❌ 在目录 {input_dir} 中未找到图像文件")
            return

        print(f"🔍 找到 {len(image_files)} 个图像文件")

        # 批量处理
        for i, image_file in enumerate(image_files, 1):
            print(f"\n📊 处理 {i}/{len(image_files)}: {image_file.name}")

            # 为每个图像创建单独的输出目录
            image_output_dir = os.path.join(output_dir, image_file.stem)

            try:
                self.visualize_all_stages(str(image_file), image_output_dir)
            except Exception as e:
                print(f"❌ 处理 {image_file.name} 时出错: {e}")
                continue

        print(f"\n✅ 批量处理完成，输出目录: {output_dir}")


def main():
    """主程序"""
    parser = argparse.ArgumentParser(
        description='MobileFaceNet + CBAM 注意力热力图可视化工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 单张图像可视化
  python attention_heatmap_visualizer.py --image path/to/image.jpg --output ./output --model model/best/model.pth

  # 批量处理
  python attention_heatmap_visualizer.py --batch --input_dir ./images --output_dir ./output --model model/best/model.pth

  # 使用CPU
  python attention_heatmap_visualizer.py --image image.jpg --output ./output --device cpu
        """
    )

    # 基本参数
    parser.add_argument('--model', type=str, default='model/best/mobilefacenet_cbam.pth',
                       help='模型权重文件路径 (默认: model/best/mobilefacenet_cbam.pth)')
    parser.add_argument('--device', type=str, default='cuda', choices=['cuda', 'cpu'],
                       help='计算设备 (默认: cuda)')

    # 单张图像模式
    parser.add_argument('--image', type=str, help='输入图像路径')
    parser.add_argument('--output', type=str, help='输出目录路径')

    # 批量处理模式
    parser.add_argument('--batch', action='store_true', help='启用批量处理模式')
    parser.add_argument('--input_dir', type=str, help='输入图像目录路径')
    parser.add_argument('--output_dir', type=str, help='输出目录路径')

    # 可选参数
    parser.add_argument('--no_individual', action='store_true',
                       help='不保存单独的阶段图像')
    parser.add_argument('--no_combined', action='store_true',
                       help='不保存组合可视化图像')
    parser.add_argument('--extensions', nargs='+',
                       default=['.jpg', '.jpeg', '.png', '.bmp', '.tiff'],
                       help='支持的图像扩展名 (默认: .jpg .jpeg .png .bmp .tiff)')

    args = parser.parse_args()

    # 参数验证
    if not args.batch and not args.image:
        parser.error("请指定 --image 或使用 --batch 模式")

    if args.batch and not args.input_dir:
        parser.error("批量模式需要指定 --input_dir")

    if not args.batch and not args.output:
        parser.error("单张图像模式需要指定 --output")

    if args.batch and not args.output_dir:
        parser.error("批量模式需要指定 --output_dir")

    # 打印配置信息
    print("=" * 60)
    print("🚀 MobileFaceNet + CBAM 注意力热力图可视化工具")
    print("=" * 60)
    print(f"📋 配置信息:")
    print(f"   模型路径: {args.model}")
    print(f"   计算设备: {args.device}")
    print(f"   模式: {'批量处理' if args.batch else '单张图像'}")

    if args.batch:
        print(f"   输入目录: {args.input_dir}")
        print(f"   输出目录: {args.output_dir}")
        print(f"   支持格式: {', '.join(args.extensions)}")
    else:
        print(f"   输入图像: {args.image}")
        print(f"   输出目录: {args.output}")

    print(f"   保存单独图像: {not args.no_individual}")
    print(f"   保存组合图像: {not args.no_combined}")
    print("=" * 60)

    try:
        # 初始化可视化器
        print("🔧 初始化可视化器...")
        visualizer = AttentionVisualizer(args.model, args.device)

        if args.batch:
            # 批量处理模式
            visualizer.batch_visualize(
                args.input_dir,
                args.output_dir,
                args.extensions
            )
        else:
            # 单张图像模式
            visualizer.visualize_all_stages(
                args.image,
                args.output,
                save_individual=not args.no_individual,
                save_combined=not args.no_combined
            )

        print("\n🎉 可视化完成！")

    except KeyboardInterrupt:
        print("\n⚠ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
