import os

# 训练配置
BATCH_SIZE = int(os.getenv('BATCH_SIZE', 256))
SAVE_FREQ = int(os.getenv('SAVE_FREQ', 1))
TEST_FREQ = int(os.getenv('TEST_FREQ', 1))
TOTAL_EPOCH = int(os.getenv('TOTAL_EPOCH', 50))

# 模型配置
RESUME = os.getenv('RESUME', '')
SAVE_DIR = os.getenv('SAVE_DIR', './model')
MODEL_PRE = os.getenv('MODEL_PRE', 'CASIA_B512_')

# 数据集配置
CASIA_DATA_DIR = os.getenv('CASIA_DATA_DIR', './data/CASIA')
LFW_DATA_DIR = os.getenv('LFW_DATA_DIR', './data/lfw')

# GPU配置
GPU_STR = os.getenv('GPU', '0,1')
if ',' in GPU_STR:
    GPU = tuple(map(int, GPU_STR.split(',')))
else:
    GPU = int(GPU_STR)

# 学习率配置
LEARNING_RATE = float(os.getenv('LEARNING_RATE', 0.1))
WEIGHT_DECAY = float(os.getenv('WEIGHT_DECAY', 4e-5))

# CBAM配置
CBAM_RATIO = int(os.getenv('CBAM_RATIO', 16))
CBAM_KERNEL_SIZE = int(os.getenv('CBAM_KERNEL_SIZE', 7))

# 数据增强配置
RANDOM_FLIP = bool(os.getenv('RANDOM_FLIP', True))
NORMALIZE_MEAN = 127.5
NORMALIZE_STD = 128.0

# 验证配置
VAL_RATIO = float(os.getenv('VAL_RATIO', 0.1))
EARLY_STOPPING_PATIENCE = int(os.getenv('EARLY_STOPPING_PATIENCE', 10))

