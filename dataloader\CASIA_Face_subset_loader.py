import numpy as np
import os
import torch
import cv2

class CASIA_Face_Subset_Loader(object):
    def __init__(self, root, subset_file):
        """
        从生成的子集文件中加载CASIA-WebFace子集
        
        Args:
            root: 数据集根目录
            subset_file: 子集图片列表文件路径（由create_casia_subset.py生成）
        """
        self.root = root
        
        # 从文件中读取子集信息
        image_list = []
        label_list = []
        unique_labels = set()
        
        # 读取子集文件
        with open(subset_file, 'r') as f:
            lines = f.read().splitlines()
            
        for line in lines:
            rel_path, label = line.split(' ')
            image_list.append(os.path.join(root, rel_path))
            label_id = int(label)
            label_list.append(label_id)
            unique_labels.add(label_id)
        
        self.image_list = image_list
        self.label_list = label_list
        self.class_nums = len(unique_labels)
        
        print(f"已加载CASIA-WebFace子集")
        print(f"身份数量: {self.class_nums}")
        print(f"图片总数: {len(self.image_list)}")

    def __getitem__(self, index):
        img_path = self.image_list[index]
        target = self.label_list[index]
        
        # 使用cv2读取图像
        img = cv2.imread(img_path)
        if img is None:
            raise ValueError(f"无法读取图像：{img_path}")
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)  # 转换颜色空间

        if len(img.shape) == 2:
            img = np.stack([img] * 3, 2)
        
        # 随机水平翻转
        flip = np.random.choice(2)*2-1
        img = img[:, ::flip, :]
        
        # 标准化处理
        img = (img - 127.5) / 128.0
        img = img.transpose(2, 0, 1)
        img = torch.from_numpy(img).float()

        return img, target

    def __len__(self):
        return len(self.image_list)

if __name__ == '__main__':
    # 测试代码
    import argparse
    
    parser = argparse.ArgumentParser(description='测试CASIA-WebFace子集加载器')
    parser.add_argument('--root', type=str, default='./data',
                        help='CASIA-WebFace数据集根目录')
    parser.add_argument('--subset-file', type=str, required=True,
                        help='子集图片列表文件路径')
    args = parser.parse_args()
    
    dataset = CASIA_Face_Subset_Loader(root=args.root, subset_file=args.subset_file)
    
    # 测试数据加载
    trainloader = torch.utils.data.DataLoader(dataset, batch_size=32, shuffle=True, num_workers=4, drop_last=False)
    print("\n测试数据加载...")
    for i, data in enumerate(trainloader):
        if i == 0:
            print(f"批次数据形状: {data[0].shape}")
            print(f"批次标签: {data[1][:10]}...")  # 显示前10个标签
            break
    
    # 统计每个类别的样本数
    label_counts = {}
    for label in dataset.label_list:
        if label not in label_counts:
            label_counts[label] = 0
        label_counts[label] += 1
    
    # 输出统计信息
    min_count = min(label_counts.values())
    max_count = max(label_counts.values())
    avg_count = sum(label_counts.values()) / len(label_counts)
    
    print(f"\n每个身份的图片数量:")
    print(f"最少: {min_count}")
    print(f"最多: {max_count}")
    print(f"平均: {avg_count:.2f}") 