#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MobileFaceNet + CBAM 智能微调脚本
针对CASIA子集进行高效微调，确保高准确率
"""

import os
import time
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple

import torch
import torch.nn as nn
import torch.optim as optim
import torch.utils.data
from torch.nn import DataParallel
from torch.optim import lr_scheduler
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from tqdm import tqdm

# 导入项目模块
from config import SAVE_DIR, CASIA_DATA_DIR, LFW_DATA_DIR
from core import model
from core.utils import init_log, AverageMeter
from dataloader.CASIA_Face_subset_loader import CASIA_Face_Subset_Loader
from dataloader.LFW_loader import LFW
from lfw_eval import parseList


def find_pretrained_model():
    """查找预训练模型"""
    possible_paths = [
        'model/best/068.ckpt',
        'model/best/best.ckpt',
        'model/best/final.ckpt'
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 搜索model目录下的所有.ckpt文件
    model_dir = Path('model')
    if model_dir.exists():
        ckpt_files = list(model_dir.rglob('*.ckpt'))
        if ckpt_files:
            return str(ckpt_files[0])

    return None


def find_subset_file():
    """查找子集文件"""
    possible_paths = [
        'output/casia_subset_images_500ids_20min_42.txt',
        'output/casia_subset_images_300ids_20min_42.txt'
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return path

    # 搜索output目录下的所有子集文件
    output_dir = Path('output')
    if output_dir.exists():
        subset_files = list(output_dir.glob('casia_subset_images_*.txt'))
        if subset_files:
            return str(subset_files[0])

    return None


def get_preset_config(preset_name):
    """获取预设配置"""
    presets = {
        'quick': {
            'description': '快速测试配置 (10轮，小批次)',
            'epochs': 10,
            'batch_size': 64,
            'lr': 0.01,
            'freeze_early_blocks': 3,
            'patience': 5,
            'progressive_unfreeze': False
        },
        'standard': {
            'description': '标准配置 (30轮，平衡设置)',
            'epochs': 30,
            'batch_size': 64,
            'lr': 0.001,
            'freeze_early_blocks': 5,
            'patience': 8,
            'progressive_unfreeze': True
        },
        'high_quality': {
            'description': '高质量配置 (50轮，大批次)',
            'epochs': 50,
            'batch_size': 64,
            'lr': 0.0005,
            'freeze_early_blocks': 7,
            'patience': 8,
            'progressive_unfreeze': True
        },
        'aggressive': {
            'description': '激进配置 (高学习率，快速收敛)',
            'epochs': 25,
            'batch_size': 64,
            'lr': 0.005,
            'freeze_early_blocks': 3,
            'patience': 8,
            'progressive_unfreeze': True
        }
    }

    return presets.get(preset_name, presets['standard'])


def setup_chinese_font():
    """设置中文字体支持"""
    font_paths = [
        'C:/Windows/Fonts/msyh.ttc',      # 微软雅黑
        'C:/Windows/Fonts/simhei.ttf',    # 黑体
        'C:/Windows/Fonts/simsun.ttc',    # 宋体
        'C:/Windows/Fonts/kaiti.ttf',     # 楷体
        '/System/Library/Fonts/PingFang.ttc',  # macOS
        '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
    ]

    font_found = False
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                font_prop = fm.FontProperties(fname=font_path)
                plt.rcParams['font.family'] = font_prop.get_name()
                plt.rcParams['axes.unicode_minus'] = False
                font_found = True
                print(f"✓ 设置中文字体: {font_path}")
                break
            except Exception:
                continue

    if not font_found:
        # 备用方案
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        print("⚠ 使用备用中文字体设置")


class SmartFineTuner:
    """智能微调器"""
    
    def __init__(self, args):
        self.args = args
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.multi_gpus = torch.cuda.device_count() > 1

        # 设置中文字体
        setup_chinese_font()

        # 创建保存目录
        self.save_dir = os.path.join(SAVE_DIR, f"SmartFineTune_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        os.makedirs(self.save_dir, exist_ok=True)

        # 初始化日志
        self.logger = init_log(self.save_dir)
        self._print = self.logger.info
        
        # 训练状态
        self.best_acc = 0.0
        self.best_loss = float('inf')
        self.patience_counter = 0
        self.training_history = {
            'train_loss': [], 'train_acc': [],
            'val_loss': [], 'val_acc': [],
            'lr': []
        }
        
        self._print("🚀 智能微调器初始化完成")
        self._print(f"📁 保存目录: {self.save_dir}")
        self._print(f"🖥️ 使用设备: {self.device}")
        self._print(f"🔢 GPU数量: {torch.cuda.device_count()}")
    
    def setup_data_loaders(self):
        """设置数据加载器"""
        self._print("📊 设置数据加载器...")

        # 训练数据集
        if self.args.subset_file and os.path.exists(self.args.subset_file):
            self._print(f"使用CASIA子集: {self.args.subset_file}")
            train_dataset = CASIA_Face_Subset_Loader(
                root=CASIA_DATA_DIR,
                subset_file=self.args.subset_file
            )
        else:
            raise FileNotFoundError(f"子集文件不存在: {self.args.subset_file}")

        # 验证数据集 (LFW)
        val_dataset = None
        if not self.args.no_validation:
            try:
                self.lfw_nl, self.lfw_nr, self.lfw_folds, self.lfw_flags = parseList(root=LFW_DATA_DIR)
                val_dataset = LFW(self.lfw_nl, self.lfw_nr)

                self.val_loader = torch.utils.data.DataLoader(
                    val_dataset,
                    batch_size=32,
                    shuffle=False,
                    num_workers=4,
                    drop_last=False,
                    pin_memory=True
                )
                self._print(f"✓ LFW验证数据集加载成功: {len(val_dataset)} 对")
            except Exception as e:
                self._print(f"⚠️ LFW验证数据集加载失败: {e}")
                self._print("将跳过验证阶段")
                self.args.no_validation = True
                self.val_loader = None
                val_dataset = None
        else:
            self.val_loader = None
            self._print("⚠️ 跳过验证阶段")

        # 训练数据加载器
        self.train_loader = torch.utils.data.DataLoader(
            train_dataset,
            batch_size=self.args.batch_size,
            shuffle=True,
            num_workers=8,
            drop_last=True,
            pin_memory=True
        )

        self.num_classes = train_dataset.class_nums
        self._print(f"✓ 训练样本数: {len(train_dataset)}")
        if val_dataset:
            self._print(f"✓ 验证样本数: {len(val_dataset)}")
        self._print(f"✓ 类别数: {self.num_classes}")
        self._print(f"✓ 批次大小: {self.args.batch_size}")
    
    def setup_model(self):
        """设置模型"""
        self._print("🏗️ 设置模型...")
        
        # 创建模型
        self.net = model.MobileFacenet()
        self.arc_margin = model.ArcMarginProduct(128, self.num_classes)
        
        # 加载预训练模型
        if os.path.exists(self.args.pretrained):
            self._print(f"📥 加载预训练模型: {self.args.pretrained}")
            checkpoint = torch.load(self.args.pretrained, map_location='cpu')
            
            # 加载主干网络参数（排除CBAM）
            model_dict = self.net.state_dict()
            pretrained_dict = {
                k: v for k, v in checkpoint['net_state_dict'].items() 
                if 'cbam' not in k and k in model_dict
            }
            model_dict.update(pretrained_dict)
            self.net.load_state_dict(model_dict)
            
            self._print(f"✓ 成功加载 {len(pretrained_dict)} 个预训练参数")
            self._print("✓ CBAM模块将随机初始化")
        else:
            self._print(f"⚠️ 预训练模型不存在: {self.args.pretrained}")
            self._print("将从头开始训练")
        
        # 移动到GPU
        self.net = self.net.to(self.device)
        self.arc_margin = self.arc_margin.to(self.device)
        
        # 多GPU支持
        if self.multi_gpus:
            self.net = DataParallel(self.net)
            self.arc_margin = DataParallel(self.arc_margin)
            self._print(f"✓ 启用多GPU训练: {torch.cuda.device_count()} GPUs")
        
        # 损失函数
        self.criterion = nn.CrossEntropyLoss()
    
    def setup_optimizer_and_scheduler(self):
        """设置优化器和学习率调度器"""
        self._print("⚙️ 设置优化器和调度器...")
        
        # 参数分组策略
        param_groups = self._create_parameter_groups()
        
        # 优化器 - 使用AdamW获得更好的收敛性
        self.optimizer = optim.AdamW([
            {'params': param_groups['frozen'], 'lr': 0.0, 'weight_decay': 0.0},  # 冻结参数
            {'params': param_groups['backbone'], 'lr': self.args.lr * 0.1, 'weight_decay': 1e-4},
            {'params': param_groups['classifier'], 'lr': self.args.lr * 0.5, 'weight_decay': 1e-3},
            {'params': param_groups['cbam'], 'lr': self.args.lr, 'weight_decay': 1e-4},  # CBAM最高学习率
        ], lr=self.args.lr, betas=(0.9, 0.999), eps=1e-8)
        
        # 学习率调度器 - 使用余弦退火
        self.scheduler = lr_scheduler.CosineAnnealingWarmRestarts(
            self.optimizer, 
            T_0=self.args.epochs // 4,  # 第一次重启周期
            T_mult=2,  # 周期倍增因子
            eta_min=self.args.lr * 0.01  # 最小学习率
        )
        
        self._print(f"✓ 优化器: AdamW")
        self._print(f"✓ 基础学习率: {self.args.lr}")
        self._print(f"✓ 调度器: CosineAnnealingWarmRestarts")
    
    def _create_parameter_groups(self) -> Dict[str, List]:
        """创建参数分组"""
        param_groups = {
            'frozen': [],      # 冻结的参数
            'backbone': [],    # 主干网络参数
            'classifier': [],  # 分类器参数
            'cbam': []        # CBAM参数
        }
        
        net = self.net.module if self.multi_gpus else self.net
        
        # 冻结早期层
        frozen_modules = ['conv1', 'dw_conv1']
        if self.args.freeze_early_blocks > 0:
            frozen_modules.extend([f'blocks.{i}' for i in range(self.args.freeze_early_blocks)])
        
        for name, param in net.named_parameters():
            if any(frozen_name in name for frozen_name in frozen_modules):
                param_groups['frozen'].append(param)
                param.requires_grad = False
            elif 'cbam' in name:
                param_groups['cbam'].append(param)
            elif 'linear1' in name or 'conv2' in name or 'linear7' in name:
                param_groups['classifier'].append(param)
            else:
                param_groups['backbone'].append(param)
        
        # ArcMargin参数
        arc_margin = self.arc_margin.module if self.multi_gpus else self.arc_margin
        param_groups['classifier'].extend(arc_margin.parameters())
        
        self._print(f"✓ 参数分组完成:")
        self._print(f"   冻结参数: {len(param_groups['frozen'])}")
        self._print(f"   主干参数: {len(param_groups['backbone'])}")
        self._print(f"   分类器参数: {len(param_groups['classifier'])}")
        self._print(f"   CBAM参数: {len(param_groups['cbam'])}")
        
        return param_groups
    
    def train_epoch(self, epoch: int) -> Tuple[float, float]:
        """训练一个epoch"""
        self.net.train()

        losses = AverageMeter('Loss', ':.4f')
        accuracies = AverageMeter('Acc', ':6.2f')

        pbar = tqdm(self.train_loader, desc=f'Epoch {epoch:3d}')

        for _, (data, target) in enumerate(pbar):
            data, target = data.to(self.device), target.to(self.device)

            # 前向传播
            features = self.net(data)
            outputs = self.arc_margin(features, target)
            loss = self.criterion(outputs, target)

            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()

            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.net.parameters(), max_norm=1.0)

            self.optimizer.step()

            # 计算准确率
            _, predicted = outputs.max(1)
            accuracy = predicted.eq(target).float().mean() * 100

            # 更新统计
            losses.update(loss.item(), data.size(0))
            accuracies.update(accuracy.item(), data.size(0))

            # 更新进度条
            pbar.set_postfix({
                'Loss': f'{losses.avg:.4f}',
                'Acc': f'{accuracies.avg:.2f}%',
                'LR': f'{self.optimizer.param_groups[-1]["lr"]:.6f}'
            })

        return losses.avg, accuracies.avg

    def validate_epoch(self) -> Tuple[float, float]:
        """验证模型性能"""
        if self.args.no_validation or self.val_loader is None:
            return 0.0, 0.0

        self.net.eval()

        with torch.no_grad():
            # LFW验证 - 按照lfw_eval.py的方式处理
            features_left = []
            features_right = []

            for _, data in enumerate(tqdm(self.val_loader, desc='Validating')):
                # LFW数据加载器返回4个tensor的列表: [left_orig, left_flip, right_orig, right_flip]
                for i in range(len(data)):
                    data[i] = data[i].to(self.device)

                # 提取特征
                res = [self.net(d).data.cpu().numpy() for d in data]

                # 按照lfw_eval.py的方式组合特征
                feature_left = np.concatenate((res[0], res[1]), 1)  # 左图原始+翻转
                feature_right = np.concatenate((res[2], res[3]), 1)  # 右图原始+翻转

                features_left.append(feature_left)
                features_right.append(feature_right)

            # 合并所有特征
            features_left = np.vstack(features_left)
            features_right = np.vstack(features_right)

            # 使用已解析的LFW数据计算准确率
            accuracy = self._compute_lfw_accuracy(features_left, features_right, self.lfw_folds, self.lfw_flags)

        return 0.0, accuracy  # 返回损失和准确率

    def _compute_lfw_accuracy(self, features_left, features_right, folds, flags):
        """计算LFW准确率"""
        folds = np.array(folds)
        flags = np.array(flags)

        # 10折交叉验证
        accs = []
        for i in range(10):
            val_fold = folds != i
            test_fold = folds == i

            # 标准化特征
            mu = np.mean(np.concatenate((features_left[val_fold], features_right[val_fold]), 0), 0)
            features_left_norm = features_left - mu
            features_right_norm = features_right - mu

            # L2归一化
            features_left_norm = features_left_norm / np.expand_dims(
                np.sqrt(np.sum(np.power(features_left_norm, 2), 1)), 1)
            features_right_norm = features_right_norm / np.expand_dims(
                np.sqrt(np.sum(np.power(features_right_norm, 2), 1)), 1)

            # 计算余弦相似度
            scores = np.sum(np.multiply(features_left_norm, features_right_norm), 1)

            # 在验证集上找最佳阈值
            threshold = self._get_threshold(scores[val_fold], flags[val_fold])

            # 在测试集上计算准确率
            acc = self._get_accuracy(scores[test_fold], flags[test_fold], threshold)
            accs.append(acc)

        return np.mean(accs)

    def _get_threshold(self, scores, flags, thr_num=10000):
        """获取最佳阈值"""
        accuracies = []
        thresholds = np.arange(-thr_num, thr_num + 1) * 1.0 / thr_num

        for threshold in thresholds:
            acc = self._get_accuracy(scores, flags, threshold)
            accuracies.append(acc)

        best_idx = np.argmax(accuracies)
        return thresholds[best_idx]

    def _get_accuracy(self, scores, flags, threshold):
        """计算给定阈值下的准确率"""
        p = np.sum(scores[flags == 1] > threshold)  # 正样本正确数
        n = np.sum(scores[flags == -1] < threshold)  # 负样本正确数
        return (p + n) / len(scores)

    def save_checkpoint(self, epoch: int, is_best: bool = False):
        """保存检查点"""
        net_state_dict = self.net.module.state_dict() if self.multi_gpus else self.net.state_dict()
        arc_state_dict = self.arc_margin.module.state_dict() if self.multi_gpus else self.arc_margin.state_dict()

        checkpoint = {
            'epoch': epoch,
            'net_state_dict': net_state_dict,
            'arc_state_dict': arc_state_dict,
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_acc': self.best_acc,
            'best_loss': self.best_loss,
            'training_history': self.training_history,
            'args': vars(self.args)
        }

        # 保存最新检查点
        torch.save(checkpoint, os.path.join(self.save_dir, 'latest.ckpt'))

        # 保存最佳模型
        if is_best:
            torch.save(checkpoint, os.path.join(self.save_dir, 'best.ckpt'))
            self._print(f"💾 保存最佳模型 (Acc: {self.best_acc:.2f}%)")

        # 定期保存
        if epoch % self.args.save_freq == 0:
            torch.save(checkpoint, os.path.join(self.save_dir, f'epoch_{epoch:03d}.ckpt'))

    def plot_training_curves(self):
        """绘制训练曲线"""
        if len(self.training_history['train_loss']) < 2:
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('训练过程监控', fontsize=16, fontweight='bold')

        epochs = range(1, len(self.training_history['train_loss']) + 1)

        # 损失曲线
        axes[0, 0].plot(epochs, self.training_history['train_loss'], 'b-', label='train loss')
        if self.training_history['val_loss'] and not self.args.no_validation:
            val_loss_filtered = [x for x in self.training_history['val_loss'] if x > 0]
            if val_loss_filtered:
                axes[0, 0].plot(epochs[:len(val_loss_filtered)], val_loss_filtered, 'r-', label='验证损失')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True)

        # 准确率曲线
        #axes[0, 1].plot(epochs, self.training_history['train_acc'], 'b-', label='训练准确率')
        if self.training_history['val_acc'] and not self.args.no_validation:
            val_acc_filtered = [x for x in self.training_history['val_acc'] if x > 0]
            if val_acc_filtered:
                axes[0, 1].plot(epochs[:len(val_acc_filtered)], val_acc_filtered, 'r-', label='val acc')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy (%)')
        axes[0, 1].legend()
        axes[0, 1].grid(True)

        # 学习率曲线
        axes[1, 0].plot(epochs, self.training_history['lr'], 'g-', label='学习率')
        axes[1, 0].set_title('学习率变化')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Learning Rate')
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        axes[1, 0].set_yscale('log')

        # 性能总结
        axes[1, 1].text(0.1, 0.9, f'最佳验证准确率: {self.best_acc:.2f}%', transform=axes[1, 1].transAxes, fontsize=12)
        axes[1, 1].text(0.1, 0.8, f'最佳验证损失: {self.best_loss:.4f}', transform=axes[1, 1].transAxes, fontsize=12)
        axes[1, 1].text(0.1, 0.7, f'当前学习率: {self.optimizer.param_groups[-1]["lr"]:.6f}', transform=axes[1, 1].transAxes, fontsize=12)
        axes[1, 1].text(0.1, 0.6, f'早停计数: {self.patience_counter}/{self.args.patience}', transform=axes[1, 1].transAxes, fontsize=12)
        axes[1, 1].set_title('训练统计')
        axes[1, 1].axis('off')

        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'training_curves.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def should_early_stop(self, val_acc: float) -> bool:
        """判断是否应该早停"""
        if val_acc > self.best_acc:
            self.patience_counter = 0
            self._print(f"🔄 新最佳准确率: {val_acc:.2f}% (之前: {self.best_acc:.2f}%), 重置patience计数器")
            return False
        else:
            self.patience_counter += 1
            self._print(f"⏳ 准确率未提升: {val_acc:.2f}% <= {self.best_acc:.2f}%, patience计数器: {self.patience_counter}/{self.args.patience}")
            return self.patience_counter >= self.args.patience

    def progressive_unfreeze(self, epoch: int):
        """渐进式解冻"""
        if not self.args.progressive_unfreeze:
            return

        net = self.net.module if self.multi_gpus else self.net

        # 在特定epoch解冻更多层
        unfreeze_schedule = {
            self.args.epochs // 4: ['blocks.0', 'blocks.1'],
            self.args.epochs // 2: ['blocks.2', 'blocks.3', 'blocks.4'],
            3 * self.args.epochs // 4: ['dw_conv1']
        }

        if epoch in unfreeze_schedule:
            modules_to_unfreeze = unfreeze_schedule[epoch]
            unfrozen_count = 0

            for name, param in net.named_parameters():
                if any(module_name in name for module_name in modules_to_unfreeze):
                    if not param.requires_grad:
                        param.requires_grad = True
                        unfrozen_count += 1

            if unfrozen_count > 0:
                self._print(f"🔓 Epoch {epoch}: 解冻 {unfrozen_count} 个参数")
                # 重新设置优化器参数组
                self.setup_optimizer_and_scheduler()

    def train(self):
        """主训练循环"""
        self._print("🎯 开始智能微调训练...")
        self._print(f"📊 训练配置:")
        self._print(f"   总轮数: {self.args.epochs}")
        self._print(f"   批次大小: {self.args.batch_size}")
        self._print(f"   初始学习率: {self.args.lr}")
        self._print(f"   早停耐心: {self.args.patience}")
        self._print(f"   渐进解冻: {self.args.progressive_unfreeze}")

        start_time = time.time()

        for epoch in range(1, self.args.epochs + 1):
            epoch_start = time.time()

            # 渐进式解冻
            self.progressive_unfreeze(epoch)

            # 训练
            train_loss, train_acc = self.train_epoch(epoch)

            # 验证
            val_loss, val_acc = self.validate_epoch()
            val_acc_percent = val_acc * 100  # 转换为百分比显示

            # 更新学习率
            self.scheduler.step()
            current_lr = self.optimizer.param_groups[-1]['lr']

            # 记录历史
            self.training_history['train_loss'].append(train_loss)
            self.training_history['train_acc'].append(train_acc)
            self.training_history['val_loss'].append(val_loss)
            self.training_history['val_acc'].append(val_acc_percent)  # 存储百分比
            self.training_history['lr'].append(current_lr)

            # 早停检查（在更新best_acc之前）
            should_stop = self.should_early_stop(val_acc_percent)

            # 检查是否为最佳模型
            is_best = val_acc_percent > self.best_acc
            if is_best:
                self.best_acc = val_acc_percent  # 存储百分比
                self.best_loss = val_loss

            # 保存检查点
            self.save_checkpoint(epoch, is_best)

            # 绘制训练曲线
            if epoch % 5 == 0:
                self.plot_training_curves()

            # 打印进度
            epoch_time = time.time() - epoch_start
            self._print(f"Epoch {epoch:3d}/{self.args.epochs} | "
                       f"Train: Loss={train_loss:.4f}, Acc={train_acc:.2f}% | "
                       f"Val: Loss={val_loss:.4f}, Acc={val_acc_percent:.2f}% | "
                       f"LR={current_lr:.6f} | Time={epoch_time:.1f}s")

            # 执行早停
            if should_stop:
                self._print(f"🛑 早停触发! 最佳验证准确率: {self.best_acc:.2f}%")
                break

        # 训练完成
        total_time = time.time() - start_time
        self._print(f"✅ 训练完成!")
        self._print(f"⏱️ 总训练时间: {total_time/3600:.2f} 小时")
        self._print(f"🏆 最佳验证准确率: {self.best_acc:.2f}%")
        self._print(f"📁 模型保存在: {self.save_dir}")

        # 最终绘图
        self.plot_training_curves()

        return self.best_acc


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='MobileFaceNet + CBAM 智能微调脚本',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 预设配置（推荐）
  python smart_finetune.py --preset standard                    # 标准配置
  python smart_finetune.py --preset quick                       # 快速测试
  python smart_finetune.py --preset high_quality               # 高质量配置
  python smart_finetune.py --preset standard --patience 20     # 覆盖参数

  # 手动配置
  python smart_finetune.py --pretrained model/best/068.ckpt --subset_file output/casia_subset_images_500ids_20min_42.txt
  python smart_finetune.py --epochs 50 --lr 0.001 --batch_size 128 --progressive_unfreeze --patience 10
        """
    )

    # 预设配置
    parser.add_argument('--preset', type=str, default=None,
                       choices=['quick', 'standard', 'high_quality', 'aggressive'],
                       help='使用预设配置 (推荐)')

    # 基本参数
    parser.add_argument('--pretrained', type=str, default=None,
                       help='预训练模型路径 (自动检测)')
    parser.add_argument('--subset_file', type=str, default=None,
                       help='CASIA子集文件路径 (自动检测)')

    # 训练参数
    parser.add_argument('--epochs', type=int, default=30,
                       help='训练轮数 (默认: 30)')
    parser.add_argument('--batch_size', type=int, default=96,
                       help='批次大小 (默认: 96)')
    parser.add_argument('--lr', type=float, default=0.001,
                       help='初始学习率 (默认: 0.001)')

    # 高级参数
    parser.add_argument('--freeze_early_blocks', type=int, default=5,
                       help='冻结的早期block数量 (默认: 5)')
    parser.add_argument('--progressive_unfreeze', action='store_true',
                       help='启用渐进式解冻策略')
    parser.add_argument('--patience', type=int, default=8,
                       help='早停耐心值 (默认: 8)')
    parser.add_argument('--save_freq', type=int, default=10,
                       help='模型保存频率 (默认: 10)')

    # 其他参数
    parser.add_argument('--no_validation', action='store_true',
                       help='跳过验证阶段（加速训练）')
    parser.add_argument('--resume', type=str, default=None,
                       help='从检查点恢复训练')
    parser.add_argument('--gpu', type=str, default=None,
                       help='指定GPU设备 (例如: 0 或 0,1)')

    return parser.parse_args()


def main():
    """主程序"""
    args = parse_args()

    # 设置GPU环境变量
    if hasattr(args, 'gpu') and args.gpu:
        os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu

    print("=" * 80)
    print("🚀 MobileFaceNet + CBAM 智能微调工具")
    print("=" * 80)

    # 处理预设配置
    if args.preset:
        print(f"📋 使用预设配置: {args.preset}")
        config = get_preset_config(args.preset)

        # 应用预设配置（只覆盖默认值，不覆盖用户指定的值）
        if args.epochs == 30:  # 默认值
            args.epochs = config['epochs']
        if args.batch_size == 96:  # 默认值
            args.batch_size = config['batch_size']
        if args.lr == 0.001:  # 默认值
            args.lr = config['lr']
        if args.freeze_early_blocks == 5:  # 默认值
            args.freeze_early_blocks = config['freeze_early_blocks']
        if args.patience == 15:  # 默认值
            args.patience = config['patience']
        if not args.progressive_unfreeze and config['progressive_unfreeze']:
            args.progressive_unfreeze = config['progressive_unfreeze']

        print(f"   描述: {config['description']}")
    else:
        print("📋 使用手动配置")
        # 默认值已在argparse中设置，无需重复设置

    # 自动检测文件路径
    if args.pretrained is None:
        args.pretrained = find_pretrained_model()
        if args.pretrained:
            print(f"🔍 自动检测到预训练模型: {args.pretrained}")
        else:
            print("❌ 未找到预训练模型，请使用 --pretrained 指定")
            return

    if args.subset_file is None:
        args.subset_file = find_subset_file()
        if args.subset_file:
            print(f"🔍 自动检测到子集文件: {args.subset_file}")
        else:
            print("❌ 未找到CASIA子集文件，请使用 --subset_file 指定")
            return

    # 打印最终配置
    print("📋 最终配置:")
    print(f"   预训练模型: {args.pretrained}")
    print(f"   子集文件: {args.subset_file}")
    print(f"   训练轮数: {args.epochs}")
    print(f"   批次大小: {args.batch_size}")
    print(f"   初始学习率: {args.lr}")
    print(f"   冻结block数: {args.freeze_early_blocks}")
    print(f"   渐进解冻: {args.progressive_unfreeze}")
    print(f"   早停耐心: {args.patience}")
    if hasattr(args, 'gpu') and args.gpu:
        print(f"   GPU设备: {args.gpu}")
    print("=" * 80)

    # 检查文件存在性
    if not os.path.exists(args.pretrained):
        print(f"❌ 预训练模型不存在: {args.pretrained}")
        return

    if not os.path.exists(args.subset_file):
        print(f"❌ 子集文件不存在: {args.subset_file}")
        return

    try:
        # 创建微调器
        fine_tuner = SmartFineTuner(args)

        # 设置数据加载器
        fine_tuner.setup_data_loaders()

        # 设置模型
        fine_tuner.setup_model()

        # 设置优化器
        fine_tuner.setup_optimizer_and_scheduler()

        # 开始训练
        best_acc = fine_tuner.train()

        print(f"\n🎉 微调完成!")
        print(f"🏆 最佳验证准确率: {best_acc:.2f}%")
        print(f"📁 模型保存目录: {fine_tuner.save_dir}")

        # 提供后续建议
        print(f"\n💡 后续步骤:")
        print(f"   1. 查看训练曲线: {fine_tuner.save_dir}/training_curves.png")
        print(f"   2. 使用最佳模型: {fine_tuner.save_dir}/best.ckpt")
        print(f"   3. 进行LFW评估: python lfw_eval.py --resume {fine_tuner.save_dir}/best.ckpt")
        print(f"   4. 生成注意力可视化: python attention_heatmap_visualizer.py --model {fine_tuner.save_dir}/best.ckpt")

    except KeyboardInterrupt:
        print("\n⚠️ 训练被用户中断")
    except Exception as e:
        print(f"\n❌ 训练过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
