from __future__ import print_function
import os
import logging
import sys
import torch
import time
from functools import wraps


def init_log(output_dir, log_level=logging.INFO):
    """
    初始化日志系统

    Args:
        output_dir: 日志输出目录
        log_level: 日志级别

    Returns:
        logging: 配置好的日志对象
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 设置控制台输出编码为UTF-8
    if sys.stdout.encoding != 'UTF-8':
        try:
            sys.stdout.reconfigure(encoding='utf-8')
        except AttributeError:
            # Python 3.6及更早版本没有reconfigure方法
            pass

    # 清除现有的处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    logging.basicConfig(level=log_level,
                        format='%(asctime)s [%(levelname)s] %(message)s',
                        datefmt='%Y-%m-%d %H:%M:%S',
                        filename=os.path.join(output_dir, 'log.log'),
                        filemode='w',
                        encoding='utf-8')  # 添加UTF-8编码

    console = logging.StreamHandler()
    console.setLevel(log_level)
    # 设置控制台日志处理器的编码
    formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s', '%Y-%m-%d %H:%M:%S')
    console.setFormatter(formatter)
    logging.getLogger('').addHandler(console)

    return logging


def check_gpu_availability():
    """
    检查GPU可用性

    Returns:
        bool: GPU是否可用
        int: 可用GPU数量
    """
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        logging.info(f"检测到 {gpu_count} 个GPU设备")
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            logging.info(f"GPU {i}: {gpu_name}, 显存: {gpu_memory:.1f}GB")
        return True, gpu_count
    else:
        logging.warning("未检测到可用的GPU设备，将使用CPU")
        return False, 0


def save_checkpoint(state, filename, is_best=False):
    """
    保存模型检查点

    Args:
        state: 模型状态字典
        filename: 保存文件名
        is_best: 是否为最佳模型
    """
    try:
        torch.save(state, filename)
        logging.info(f"模型检查点已保存: {filename}")

        if is_best:
            best_filename = os.path.join(os.path.dirname(filename), 'best.ckpt')
            torch.save(state, best_filename)
            logging.info(f"最佳模型已保存: {best_filename}")
    except Exception as e:
        logging.error(f"保存模型检查点失败: {e}")
        raise


def load_checkpoint(filename, model, optimizer=None):
    """
    加载模型检查点

    Args:
        filename: 检查点文件名
        model: 模型对象
        optimizer: 优化器对象（可选）

    Returns:
        int: 开始的epoch数
    """
    try:
        if not os.path.exists(filename):
            raise FileNotFoundError(f"检查点文件不存在: {filename}")

        checkpoint = torch.load(filename, map_location='cpu')

        # 加载模型状态
        if 'net_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['net_state_dict'])
        elif 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)

        # 加载优化器状态
        if optimizer is not None and 'optimizer_state_dict' in checkpoint:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

        start_epoch = checkpoint.get('epoch', 0) + 1
        logging.info(f"成功加载检查点: {filename}, 开始epoch: {start_epoch}")

        return start_epoch
    except Exception as e:
        logging.error(f"加载检查点失败: {e}")
        raise


def timer(func):
    """
    计时装饰器
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logging.info(f"{func.__name__} 执行时间: {end_time - start_time:.2f}秒")
        return result
    return wrapper


class AverageMeter:
    """
    计算和存储平均值和当前值
    """
    def __init__(self, name, fmt=':f'):
        self.name = name
        self.fmt = fmt
        self.reset()

    def reset(self):
        self.val = 0
        self.avg = 0
        self.sum = 0
        self.count = 0

    def update(self, val, n=1):
        self.val = val
        self.sum += val * n
        self.count += n
        self.avg = self.sum / self.count

    def __str__(self):
        fmtstr = '{name} {val' + self.fmt + '} ({avg' + self.fmt + '})'
        return fmtstr.format(**self.__dict__)


def validate_config():
    """
    验证配置文件的有效性
    """
    from config import CASIA_DATA_DIR, LFW_DATA_DIR, SAVE_DIR

    # 检查数据目录
    if not os.path.exists(CASIA_DATA_DIR):
        logging.warning(f"CASIA数据集目录不存在: {CASIA_DATA_DIR}")

    if not os.path.exists(LFW_DATA_DIR):
        logging.warning(f"LFW数据集目录不存在: {LFW_DATA_DIR}")

    # 创建保存目录
    os.makedirs(SAVE_DIR, exist_ok=True)
    logging.info(f"模型保存目录: {SAVE_DIR}")


if __name__ == '__main__':
    pass
