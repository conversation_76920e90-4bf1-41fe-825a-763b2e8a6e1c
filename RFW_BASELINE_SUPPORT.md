# RFW评估脚本 - 基准模型支持

## 新增功能概述

为RFW评估脚本添加了对基准MobileFaceNet模型（不带CBAM）的完整支持，现在可以：

1. **评估基准模型**: 评估原始MobileFaceNet模型在RFW数据集上的性能
2. **评估CBAM模型**: 评估带CBAM注意力机制的MobileFaceNet模型
3. **自动模型检测**: 根据模型路径和权重自动识别模型类型
4. **性能比较**: 直接比较基准模型和CBAM模型的性能差异

## 支持的模型类型

### 1. 基准模型 (BaseMobileFacenet)
- **描述**: 原始MobileFaceNet架构，不包含CBAM模块
- **模型路径**: `model/mobilefacenet_best/068.ckpt`
- **特点**: 轻量级、高效的人脸识别基准模型

### 2. CBAM模型 (MobileFacenet)
- **描述**: 在MobileFaceNet基础上添加了CBAM注意力机制
- **模型路径**: `model/SmartFineTune_*/best.ckpt` 或 `model/MobileFaceNet_CBAM_*/best.ckpt`
- **特点**: 通过注意力机制提升特征表示能力

## 核心技术实现

### 1. 模型架构适配
```python
class BaseMobileFacenet(model.MobileFacenet):
    """基准MobileFaceNet模型（不带CBAM模块）"""
    
    def __init__(self, bottleneck_setting=model.Mobilefacenet_bottleneck_setting):
        # 继承基础架构但不添加CBAM模块
        super(model.MobileFacenet, self).__init__()
        
        # 标准MobileFaceNet层
        self.conv1 = model.ConvBlock(3, 64, 3, 2, 1)
        self.dw_conv1 = model.ConvBlock(64, 64, 3, 1, 1, dw=True)
        self.blocks = self._make_layer(block, bottleneck_setting)
        
        # 不添加CBAM模块
        self.conv2 = model.ConvBlock(128, 512, 1, 1, 0)
        self.linear7 = model.ConvBlock(512, 512, (7, 6), 1, 0, dw=True, linear=True)
        self.linear1 = model.ConvBlock(512, 128, 1, 1, 0, linear=True)
```

### 2. 智能模型检测
```python
def _detect_model_type(self):
    """检测模型类型（是否包含CBAM模块）"""
    
    # 1. 路径特征检测
    if 'mobilefacenet_best' in model_path_lower:
        return 'base'
    if 'cbam' in model_path_lower or 'smartfinetune' in model_path_lower:
        return 'cbam'
    
    # 2. 权重键检测
    checkpoint = torch.load(self.model_path, map_location='cpu')
    state_dict = checkpoint.get('net_state_dict', checkpoint)
    cbam_keys = [key for key in state_dict.keys() if 'cbam' in key.lower()]
    
    return 'cbam' if cbam_keys else 'base'
```

### 3. 兼容性权重加载
```python
def _load_model(self):
    """加载预训练模型"""
    
    # 根据检测类型创建模型
    if self.detected_model_type == 'cbam':
        net = model.MobileFacenet()
    else:
        net = BaseMobileFacenet()
    
    # 智能权重过滤
    if self.detected_model_type == 'base':
        filtered_state_dict = {k: v for k, v in state_dict.items() 
                             if 'cbam' not in k.lower()}
        state_dict = filtered_state_dict
    
    # 允许部分匹配的权重加载
    net.load_state_dict(state_dict, strict=False)
```

## 使用方法

### 1. 快速开始
```bash
# 交互式选择界面
python run_rfw_evaluation.py

# 直接比较两种模型
python compare_baseline_cbam.py
```

### 2. 命令行参数
```bash
# 评估基准模型
python rfw_evaluation.py --model_type base

# 评估CBAM模型
python rfw_evaluation.py --model_type cbam

# 自动检测模型类型
python rfw_evaluation.py --model_type auto

# 比较两种模型
python rfw_evaluation.py --compare_models
```

### 3. 编程接口
```python
from rfw_evaluation import RFWEvaluator, find_baseline_model, find_cbam_model

# 评估基准模型
base_model_path = find_baseline_model()
evaluator = RFWEvaluator(base_model_path, model_type='base')
results = evaluator.evaluate_all_races()

# 评估CBAM模型
cbam_model_path = find_cbam_model()
evaluator = RFWEvaluator(cbam_model_path, model_type='cbam')
results = evaluator.evaluate_all_races()
```

## 输出结果

### 1. 单模型评估
- 各种族的准确率、AUC、精确率、召回率、F1分数
- ROC曲线图和准确率对比图
- 详细的CSV结果文件

### 2. 模型比较
- 并排比较两种模型在各种族上的性能
- 性能提升/下降的量化分析
- 比较结果的可视化图表

### 3. 示例输出
```
📊 模型性能比较报告
================================================================================

种族          基准准确率      CBAM准确率      提升        基准AUC     CBAM AUC    AUC提升    
--------------------------------------------------------------------------------
African      91.23      93.45      +2.22      94.56      96.78      +2.22     
Asian        93.67      95.12      +1.45      95.89      97.23      +1.34     
Caucasian    94.12      95.89      +1.77      96.45      98.12      +1.67     
Indian       90.78      92.34      +1.56      94.02      95.67      +1.65     
--------------------------------------------------------------------------------
平均          92.45      94.20      +1.75      95.23      96.95      +1.72     

📈 总体性能提升:
   准确率提升: +1.75%
   AUC提升: +1.72%
   ✅ CBAM模块显著提升了模型性能
```

## 文件结构

```
├── rfw_evaluation.py              # 主评估脚本（支持两种模型）
├── run_rfw_evaluation.py          # 交互式运行脚本
├── compare_baseline_cbam.py       # 专门的模型比较脚本
├── rfw_evaluation_examples.py     # 使用示例脚本
├── RFW_EVALUATION_README.md       # 详细使用说明
└── RFW_BASELINE_SUPPORT.md        # 本文档
```

## 技术优势

1. **向后兼容**: 完全兼容现有的CBAM模型评估功能
2. **智能检测**: 自动识别模型类型，无需手动指定
3. **灵活配置**: 支持多种使用方式和参数配置
4. **详细比较**: 提供全面的性能比较分析
5. **易于使用**: 提供交互式界面和示例代码

## 应用场景

1. **基准测试**: 评估基础MobileFaceNet模型性能
2. **改进验证**: 验证CBAM模块的有效性
3. **模型选择**: 根据性能需求选择合适的模型
4. **研究分析**: 深入分析注意力机制的作用效果

这个扩展功能为RFW数据集评估提供了完整的基准模型支持，使得可以全面评估和比较不同模型架构的性能表现。
