#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建人脸验证对比图表
根据CSV结果文件生成可视化对比图

"""

import os
import sys
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from PIL import Image
import numpy as np
from datetime import datetime

# 解决OpenMP冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def load_verification_results(csv_path):
    """加载验证结果CSV文件"""
    try:
        df = pd.read_csv(csv_path, encoding='utf-8')
        print(f"✅ 成功加载验证结果: {csv_path}")
        print(f"   总记录数: {len(df)}")
        return df
    except Exception as e:
        print(f"❌ 加载CSV文件失败: {e}")
        return None

def load_and_resize_image(image_path, target_size=(120, 140)):
    """加载并调整图片大小"""
    try:
        if not os.path.exists(image_path):
            print(f"⚠️  图片不存在: {image_path}")
            # 创建一个占位图片
            img = Image.new('RGB', target_size, color='lightgray')
            return img

        img = Image.open(image_path)
        img = img.convert('RGB')
        img = img.resize(target_size, Image.Resampling.LANCZOS)
        return img
    except Exception as e:
        print(f"❌ 加载图片失败 {image_path}: {e}")
        # 创建一个占位图片
        img = Image.new('RGB', target_size, color='lightgray')
        return img

def create_comparison_chart(df, dataset_path, output_path=None):
    """创建对比图表"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 按明星分组
    celebrities = df['celebrity'].unique()
    print(f"📊 创建对比图表，包含 {len(celebrities)} 个明星")
    
    # 计算图表尺寸
    max_images_per_row = max(len(df[df['celebrity'] == celebrity]) + 1 for celebrity in celebrities)
    fig_width = max_images_per_row * 1.8
    fig_height = len(celebrities) * 2.2

    fig, axes = plt.subplots(len(celebrities), max_images_per_row,
                            figsize=(fig_width, fig_height))
    
    # 确保axes是二维数组
    if len(celebrities) == 1:
        axes = axes.reshape(1, -1)
    elif max_images_per_row == 1:
        axes = axes.reshape(-1, 1)
    
    for row_idx, celebrity in enumerate(celebrities):
        celebrity_data = df[df['celebrity'] == celebrity].sort_values('test_photo')
        
        print(f"   处理明星: {celebrity} ({len(celebrity_data)} 张测试图片)")
        
        # 获取基准图片路径
        lifestyle_photo = celebrity_data.iloc[0]['lifestyle_photo']
        lifestyle_path = os.path.join(dataset_path, celebrity, lifestyle_photo)
        
        # 显示基准图片
        lifestyle_img = load_and_resize_image(lifestyle_path)
        axes[row_idx, 0].imshow(lifestyle_img)
        axes[row_idx, 0].axis('off')

        # 添加边框（基准图用蓝色边框）
        rect = patches.Rectangle((0, 0), lifestyle_img.width-1, lifestyle_img.height-1,
                               linewidth=3, edgecolor='blue', facecolor='none')
        axes[row_idx, 0].add_patch(rect)
        
        # 显示测试图片
        for col_idx, (_, row) in enumerate(celebrity_data.iterrows(), 1):
            test_photo = row['test_photo']
            similarity = row['similarity']
            ground_truth = row['ground_truth']
            prediction = row['prediction']
            correct = row['correct']
            
            test_path = os.path.join(dataset_path, celebrity, test_photo)
            test_img = load_and_resize_image(test_path)

            axes[row_idx, col_idx].imshow(test_img)
            axes[row_idx, col_idx].axis('off')

            # 根据预测结果设置边框颜色
            if correct:
                border_color = 'green'  # 正确预测
            else:
                border_color = 'red'    # 错误预测

            # 添加边框
            rect = patches.Rectangle((0, 0), test_img.width-1, test_img.height-1,
                                   linewidth=3, edgecolor=border_color, facecolor='none')
            axes[row_idx, col_idx].add_patch(rect)

            # 在图片下方添加相似度标签
            similarity_text = f'Similarity={similarity:.2f}'
            axes[row_idx, col_idx].text(0.5, -0.05, similarity_text,
                                      transform=axes[row_idx, col_idx].transAxes,
                                      ha='center', va='top', fontsize=10,
                                      fontweight='bold', color='black')
        
        # 隐藏多余的子图
        for col_idx in range(len(celebrity_data) + 1, max_images_per_row):
            axes[row_idx, col_idx].axis('off')
    
    # 调整布局 - 增加行间距，去掉图例
    plt.tight_layout()
    plt.subplots_adjust(top=0.98, hspace=0.25, wspace=0.05)
    
    # 保存图片
    if output_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = f"verification_comparison_chart_{timestamp}.png"
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    print(f"📊 对比图表已保存: {output_path}")
    
    # 显示图片
    plt.show()
    
    return output_path

def create_summary_statistics(df):
    """创建统计摘要"""
    print(f"\n📈 验证结果统计摘要:")
    print(f"="*50)
    
    total_pairs = len(df)
    correct_predictions = df['correct'].sum()
    accuracy = (correct_predictions / total_pairs) * 100
    
    print(f"总图片对数量: {total_pairs}")
    print(f"正确预测数量: {correct_predictions}")
    print(f"验证准确率: {accuracy:.2f}%")
    
    # 按明星统计
    print(f"\n👤 按明星统计:")
    for celebrity in df['celebrity'].unique():
        celebrity_data = df[df['celebrity'] == celebrity]
        celebrity_correct = celebrity_data['correct'].sum()
        celebrity_total = len(celebrity_data)
        celebrity_accuracy = (celebrity_correct / celebrity_total) * 100
        print(f"   {celebrity}: {celebrity_correct}/{celebrity_total} ({celebrity_accuracy:.1f}%)")
    
    # 相似度统计
    similarities = df['similarity']
    print(f"\n📊 相似度统计:")
    print(f"   平均值: {similarities.mean():.4f}")
    print(f"   标准差: {similarities.std():.4f}")
    print(f"   最小值: {similarities.min():.4f}")
    print(f"   最大值: {similarities.max():.4f}")
    print(f"   中位数: {similarities.median():.4f}")
    
    # 正负样本分析
    positive_samples = df[df['ground_truth'] == True]
    negative_samples = df[df['ground_truth'] == False]
    
    print(f"\n🔍 正负样本分析:")
    print(f"   正样本数量: {len(positive_samples)} (平均相似度: {positive_samples['similarity'].mean():.4f})")
    print(f"   负样本数量: {len(negative_samples)} (平均相似度: {negative_samples['similarity'].mean():.4f})")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="创建人脸验证对比图表")
    parser.add_argument('--csv', type=str, 
                       default='verification_results/celebrity_verification_20250630_170100.csv',
                       help='验证结果CSV文件路径')
    parser.add_argument('--dataset', type=str, default='data/makeup_dataset',
                       help='数据集路径')
    parser.add_argument('--output', type=str, default=None,
                       help='输出图片路径')
    
    args = parser.parse_args()
    
    print("🎨 创建人脸验证对比图表")
    print("="*50)
    
    # 检查文件是否存在
    if not os.path.exists(args.csv):
        print(f"❌ CSV文件不存在: {args.csv}")
        sys.exit(1)
    
    if not os.path.exists(args.dataset):
        print(f"❌ 数据集路径不存在: {args.dataset}")
        sys.exit(1)
    
    try:
        # 加载验证结果
        df = load_verification_results(args.csv)
        if df is None:
            sys.exit(1)
        
        # 创建统计摘要
        create_summary_statistics(df)
        
        # 创建对比图表
        output_path = create_comparison_chart(df, args.dataset, args.output)
        
        print(f"\n🎉 图表创建完成!")
        print(f"📁 输出文件: {output_path}")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
