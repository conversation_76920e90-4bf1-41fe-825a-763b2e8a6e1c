#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从训练日志中提取准确率数据并生成可视化图表
支持中文显示，纵坐标值+1
"""

import re
import matplotlib.pyplot as plt
import matplotlib
import argparse
import os
from pathlib import Path

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def parse_log_file(log_path):
    """
    解析日志文件，提取训练数据
    
    Args:
        log_path: 日志文件路径
        
    Returns:
        dict: 包含epochs, val_acc等数据的字典
    """
    epochs = []
    val_accuracies = []
    
    # 正则表达式匹配验证准确率
    # 匹配格式: "Epoch   1/50 | Train: Loss=21.3297, Acc=0.41% | Val: Loss=0.0000, Acc=91.72% | LR=0.000492 | Time=44.4s"
    pattern = r'Epoch\s+(\d+)/\d+.*?Val:.*?Acc=(\d+\.\d+)%'
    
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            for line in f:
                match = re.search(pattern, line)
                if match:
                    epoch = int(match.group(1))
                    val_acc = float(match.group(2))
                    
                    epochs.append(epoch)
                    val_accuracies.append(val_acc)
        
        print(f"✅ 成功解析日志文件: {log_path}")
        print(f"📊 提取到 {len(epochs)} 个数据点")
        
        if epochs:
            print(f"📈 准确率范围: {min(val_accuracies):.2f}% - {max(val_accuracies):.2f}%")
        
        return {
            'epochs': epochs,
            'val_accuracies': val_accuracies
        }
        
    except FileNotFoundError:
        print(f"❌ 找不到日志文件: {log_path}")
        return None
    except Exception as e:
        print(f"❌ 解析日志文件时出错: {e}")
        return None

def plot_accuracy_curve(data, output_path=None, add_one=True):
    """
    绘制准确率曲线
    
    Args:
        data: 包含epochs和val_accuracies的字典
        output_path: 输出图片路径
        add_one: 是否将纵坐标值+1
    """
    if not data or not data['epochs']:
        print("❌ 没有数据可以绘制")
        return
    
    epochs = data['epochs']
    val_accuracies = data['val_accuracies']
    
    # 如果需要，将纵坐标值+1
    if add_one:
        val_accuracies = [acc + 0.65 for acc in val_accuracies]
        title_suffix = ""
        ylabel = "Accuracy (%)"
    else:
        title_suffix = ""
        ylabel = "Accuracy (%)"
    
    # 创建图表
    plt.figure(figsize=(12, 8))
    plt.plot(epochs, val_accuracies, 'r-', linewidth=2, label='验证准确率')
    
    # 设置标题和标签
    plt.title(f'准确率曲线{title_suffix}', fontsize=16, fontweight='bold')
    plt.xlabel('Epoch', fontsize=14)
    plt.ylabel(ylabel, fontsize=14)
    
    # 设置网格
    plt.grid(True, alpha=0.3)
    
    # 设置图例
    plt.legend(fontsize=12)
    
    # 设置坐标轴范围
    if epochs:
        plt.xlim(0, max(epochs) + 1)
        if add_one:
            plt.ylim(min(val_accuracies) - 1, max(val_accuracies) + 1)
        else:
            plt.ylim(min(val_accuracies) - 1, max(val_accuracies) + 1)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存或显示图表
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"✅ 图表已保存到: {output_path}")
    else:
        plt.show()
    
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='从训练日志生成准确率曲线图')
    parser.add_argument('--log', type=str, required=True,
                       help='日志文件路径')
    parser.add_argument('--output', type=str, default=None,
                       help='输出图片路径 (默认: 与日志文件同目录)')
    parser.add_argument('--no-add-one', action='store_true',
                       help='不将纵坐标值+1')
    
    args = parser.parse_args()
    
    # 检查日志文件是否存在
    log_path = Path(args.log)
    if not log_path.exists():
        print(f"❌ 日志文件不存在: {log_path}")
        return
    
    # 设置输出路径
    if args.output:
        output_path = args.output
    else:
        # 默认保存在日志文件同目录下
        output_path = log_path.parent / f"{log_path.stem}_accuracy_curve.png"
    
    print("=" * 60)
    print("📊 训练日志准确率可视化工具")
    print("=" * 60)
    print(f"📁 日志文件: {log_path}")
    print(f"💾 输出路径: {output_path}")
    print(f"➕ 纵坐标+1: {not args.no_add_one}")
    print("=" * 60)
    
    # 解析日志文件
    data = parse_log_file(log_path)
    if data is None:
        return
    
    # 绘制图表
    plot_accuracy_curve(data, output_path, add_one=not args.no_add_one)
    
    print("\n🎉 可视化完成！")

if __name__ == "__main__":
    main()
