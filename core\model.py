from torch import nn
import torch
import torch.nn.functional as F
from torch.autograd import Variable

import math
from torch.nn import Parameter
from .cbam import CBAM

class Bottleneck(nn.Module):
    def __init__(self, inp, oup, stride, expansion):
        super(Bottleneck, self).__init__()
        self.connect = stride == 1 and inp == oup
        #
        self.conv = nn.Sequential(
            #pw
            nn.Conv2d(inp, inp * expansion, 1, 1, 0, bias=False),
            nn.BatchNorm2d(inp * expansion),
            nn.PReLU(inp * expansion),
            # nn.ReLU(inplace=True),

            #dw
            nn.Conv2d(inp * expansion, inp * expansion, 3, stride, 1, groups=inp * expansion, bias=False),
            nn.BatchNorm2d(inp * expansion),
            nn.PReLU(inp * expansion),
            # nn.ReLU(inplace=True),

            #pw-linear
            nn.Conv2d(inp * expansion, oup, 1, 1, 0, bias=False),
            nn.BatchNorm2d(oup),
        )

    def forward(self, x):
        if self.connect:
            return x + self.conv(x)
        else:
            return self.conv(x)

class ConvBlock(nn.Module):
    def __init__(self, inp, oup, k, s, p, dw=False, linear=False):
        super(ConvBlock, self).__init__()
        self.linear = linear
        if dw:
            self.conv = nn.Conv2d(inp, oup, k, s, p, groups=inp, bias=False)
        else:
            self.conv = nn.Conv2d(inp, oup, k, s, p, bias=False)
        self.bn = nn.BatchNorm2d(oup)
        if not linear:
            self.prelu = nn.PReLU(oup)
    def forward(self, x):
        x = self.conv(x)
        x = self.bn(x)
        if self.linear:
            return x
        else:
            return self.prelu(x)

Mobilefacenet_bottleneck_setting = [
    # t, c , n ,s
    [2, 64, 5, 2],
    [4, 128, 1, 2],
    [2, 128, 6, 1],
    [4, 128, 1, 2],
    [2, 128, 2, 1]
]

Mobilenetv2_bottleneck_setting = [
    # t, c, n, s
    [1, 16, 1, 1],
    [6, 24, 2, 2],
    [6, 32, 3, 2],
    [6, 64, 4, 2],
    [6, 96, 3, 1],
    [6, 160, 3, 2],
    [6, 320, 1, 1],
]

class MobileFacenet(nn.Module):
    def __init__(self, bottleneck_setting=Mobilefacenet_bottleneck_setting):
        super(MobileFacenet, self).__init__()

        self.conv1 = ConvBlock(3, 64, 3, 2, 1)

        self.dw_conv1 = ConvBlock(64, 64, 3, 1, 1, dw=True)

        self.inplanes = 64
        block = Bottleneck
        self.blocks = self._make_layer(block, bottleneck_setting)

        # 添加CBAM模块
        # 参考图表中的特征图尺寸和通道数
        self.cbam1 = CBAM(128, ratio=16, kernel_size=7)  # 第一个CBAM模块，14x12x128
        self.cbam2 = CBAM(128, ratio=16, kernel_size=7)  # 第二个CBAM模块，14x12x128
        self.cbam3 = CBAM(128, ratio=16, kernel_size=7)  # 第三个CBAM模块，7x6x128

        self.conv2 = ConvBlock(128, 512, 1, 1, 0)

        # 对于112x96的输入，最终特征图尺寸为7x6
        self.linear7 = ConvBlock(512, 512, (7, 6), 1, 0, dw=True, linear=True)

        self.linear1 = ConvBlock(512, 128, 1, 1, 0, linear=True)

        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                n = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
                m.weight.data.normal_(0, math.sqrt(2. / n))
            elif isinstance(m, nn.BatchNorm2d):
                m.weight.data.fill_(1)
                m.bias.data.zero_()

    def _make_layer(self, block, setting):
        layers = []
        for t, c, n, s in setting:
            for i in range(n):
                if i == 0:
                    layers.append(block(self.inplanes, c, s, t))
                else:
                    layers.append(block(self.inplanes, c, 1, t))
                self.inplanes = c

        return nn.ModuleList(layers)

    def forward(self, x):
        x = self.conv1(x)  # 112x96 -> 56x48
        x = self.dw_conv1(x)  # 56x48 -> 56x48
        
        # 通过第一个bottleneck block (5层) - [2, 64, 5, 2]
        for i in range(5):
            x = self.blocks[i](x)  # 56x48 -> 28x24
        
        # 通过第二个bottleneck block (1层) - [4, 128, 1, 2]
        x = self.blocks[5](x)  # 28x24 -> 14x12, 通道数变为128
        
        # 插入第一个CBAM，特征图尺寸为14x12x128
        x = self.cbam1(x)
        
        # 通过第三个bottleneck block (6层) - [2, 128, 6, 1]
        for i in range(6, 12):
            x = self.blocks[i](x)  # 14x12 -> 14x12, 通道数保持128

        # 插入第二个CBAM，特征图尺寸为14x12x128
        x = self.cbam2(x)
        
        # 通过第四个bottleneck block (1层) - [4, 128, 1, 2]
        x = self.blocks[12](x)  # 14x12 -> 7x6, 通道数保持128

        # 插入第三个CBAM，特征图尺寸为7x6x512
        x = self.cbam3(x)
        
        
        # 通过第五个bottleneck block (2层) - [2, 128, 2, 1]
        for i in range(13, 15):
            x = self.blocks[i](x)  # 7x6 -> 7x6, 通道数保持128
        
        # 1x1卷积
        x = self.conv2(x)  # 7x6 -> 7x6, 通道数变为512
        
        x = self.linear7(x)  # 7x6 -> 1x1, 通道数保持512
        x = self.linear1(x)  # 1x1 -> 1x1, 通道数变为128
        x = x.view(x.size(0), -1)

        return x


class ArcMarginProduct(nn.Module):
    def __init__(self, in_features=128, out_features=200, s=32.0, m=0.50, easy_margin=False):
        super(ArcMarginProduct, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.s = s
        self.m = m
        self.weight = Parameter(torch.Tensor(out_features, in_features))
        nn.init.xavier_uniform_(self.weight)

        self.easy_margin = easy_margin
        self.cos_m = math.cos(m)
        self.sin_m = math.sin(m)
        # make the function cos(theta+m) monotonic decreasing while theta in [0°,180°]
        self.th = math.cos(math.pi - m)
        self.mm = math.sin(math.pi - m) * m

    def forward(self, x, label):
        cosine = F.linear(F.normalize(x), F.normalize(self.weight))
        sine = torch.sqrt(1.0 - torch.pow(cosine, 2))
        phi = cosine * self.cos_m - sine * self.sin_m
        if self.easy_margin:
            phi = torch.where(cosine > 0, phi, cosine)
        else:
            phi = torch.where((cosine - self.th) > 0, phi, cosine - self.mm)

        one_hot = torch.zeros(cosine.size(), device='cuda')
        one_hot.scatter_(1, label.view(-1, 1).long(), 1)
        output = (one_hot * phi) + ((1.0 - one_hot) * cosine)
        output *= self.s
        return output


if __name__ == "__main__":
    input = Variable(torch.FloatTensor(2, 3, 112, 96))
    net = MobileFacenet()
    print(net)
    x = net(input)
    print(x.shape)
