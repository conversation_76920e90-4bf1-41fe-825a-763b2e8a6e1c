#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找最佳相似度阈值脚本
通过测试不同阈值来找到最佳的人脸验证性能

作者: AI Assistant
日期: 2025-06-30
"""

import os
import sys
import subprocess
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 解决OpenMP冲突问题
os.environ['KMP_DUPLICATE_LIB_OK'] = 'TRUE'

def test_threshold(model_path, dataset_path, threshold):
    """测试指定阈值的性能"""
    try:
        cmd = [
            "python", "celebrity_makeup_verification.py",
            "--dataset", dataset_path,
            "--model", model_path,
            "--threshold", str(threshold),
            "--no_save"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            output = result.stdout
            
            # 解析结果
            metrics = {}
            for line in output.split('\n'):
                if "验证准确率:" in line:
                    metrics['accuracy'] = float(line.split(':')[1].strip().replace('%', ''))
                elif "精确率 (Precision):" in line:
                    metrics['precision'] = float(line.split(':')[1].strip().replace('%', ''))
                elif "召回率 (Recall):" in line:
                    metrics['recall'] = float(line.split(':')[1].strip().replace('%', ''))
                elif "F1分数 (F1-Score):" in line:
                    metrics['f1_score'] = float(line.split(':')[1].strip())
                elif "真正例 (TP):" in line:
                    metrics['tp'] = int(line.split(':')[1].split('-')[0].strip())
                elif "真负例 (TN):" in line:
                    metrics['tn'] = int(line.split(':')[1].split('-')[0].strip())
                elif "假正例 (FP):" in line:
                    metrics['fp'] = int(line.split(':')[1].split('-')[0].strip())
                elif "假负例 (FN):" in line:
                    metrics['fn'] = int(line.split(':')[1].split('-')[0].strip())
            
            return metrics
        else:
            print(f"❌ 阈值 {threshold} 测试失败")
            return None
    
    except Exception as e:
        print(f"❌ 阈值 {threshold} 测试异常: {e}")
        return None

def find_optimal_threshold(model_path, dataset_path, threshold_range=(0.1, 0.9), step=0.05):
    """寻找最佳阈值"""
    print(f"🔍 寻找最佳相似度阈值")
    print(f"📁 数据集: {dataset_path}")
    print(f"🤖 模型: {model_path}")
    print(f"🎯 阈值范围: {threshold_range[0]} - {threshold_range[1]}, 步长: {step}")
    print("="*60)
    
    # 生成阈值列表
    thresholds = np.arange(threshold_range[0], threshold_range[1] + step, step)
    thresholds = [round(t, 3) for t in thresholds]
    
    results = []
    
    for threshold in thresholds:
        print(f"🧪 测试阈值: {threshold}")
        metrics = test_threshold(model_path, dataset_path, threshold)
        
        if metrics:
            metrics['threshold'] = threshold
            results.append(metrics)
            print(f"   准确率: {metrics.get('accuracy', 0):.1f}%, F1: {metrics.get('f1_score', 0):.2f}")
        else:
            print(f"   测试失败")
    
    if not results:
        print("❌ 没有成功的测试结果")
        return None
    
    # 分析结果
    print(f"\n📊 测试完成，共 {len(results)} 个有效结果")
    
    # 找到最佳阈值（基于不同指标）
    best_accuracy = max(results, key=lambda x: x.get('accuracy', 0))
    best_f1 = max(results, key=lambda x: x.get('f1_score', 0))
    
    print(f"\n🏆 最佳结果:")
    print(f"   最高准确率: 阈值={best_accuracy['threshold']}, 准确率={best_accuracy.get('accuracy', 0):.1f}%")
    print(f"   最高F1分数: 阈值={best_f1['threshold']}, F1={best_f1.get('f1_score', 0):.2f}")
    
    # 绘制结果图表
    plot_threshold_analysis(results, model_path)
    
    # 保存结果
    save_threshold_analysis(results, model_path, dataset_path)
    
    return results

def plot_threshold_analysis(results, model_path):
    """绘制阈值分析图表"""
    try:
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        thresholds = [r['threshold'] for r in results]
        accuracies = [r.get('accuracy', 0) for r in results]
        precisions = [r.get('precision', 0) for r in results]
        recalls = [r.get('recall', 0) for r in results]
        f1_scores = [r.get('f1_score', 0) for r in results]
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # 图1: 准确率曲线
        ax1.plot(thresholds, accuracies, 'b-o', linewidth=2, markersize=4)
        ax1.set_xlabel('相似度阈值')
        ax1.set_ylabel('准确率 (%)')
        ax1.set_title('准确率 vs 阈值')
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 100)
        
        # 标注最佳点
        best_acc_idx = accuracies.index(max(accuracies))
        ax1.annotate(f'最佳: {thresholds[best_acc_idx]:.2f}', 
                    xy=(thresholds[best_acc_idx], accuracies[best_acc_idx]),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        # 图2: 精确率和召回率
        ax2.plot(thresholds, precisions, 'r-o', label='精确率', linewidth=2, markersize=4)
        ax2.plot(thresholds, recalls, 'g-o', label='召回率', linewidth=2, markersize=4)
        ax2.set_xlabel('相似度阈值')
        ax2.set_ylabel('百分比 (%)')
        ax2.set_title('精确率和召回率 vs 阈值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 100)
        
        # 图3: F1分数
        ax3.plot(thresholds, f1_scores, 'm-o', linewidth=2, markersize=4)
        ax3.set_xlabel('相似度阈值')
        ax3.set_ylabel('F1分数')
        ax3.set_title('F1分数 vs 阈值')
        ax3.grid(True, alpha=0.3)
        
        # 标注最佳F1点
        best_f1_idx = f1_scores.index(max(f1_scores))
        ax3.annotate(f'最佳: {thresholds[best_f1_idx]:.2f}', 
                    xy=(thresholds[best_f1_idx], f1_scores[best_f1_idx]),
                    xytext=(10, 10), textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                    arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0'))
        
        # 图4: 综合对比
        ax4.plot(thresholds, accuracies, 'b-', label='准确率', linewidth=2)
        ax4.plot(thresholds, precisions, 'r-', label='精确率', linewidth=2)
        ax4.plot(thresholds, recalls, 'g-', label='召回率', linewidth=2)
        # F1分数需要缩放到0-100范围以便比较
        f1_scaled = [f * 100 / max(f1_scores) if max(f1_scores) > 0 else 0 for f in f1_scores]
        ax4.plot(thresholds, f1_scaled, 'm-', label='F1分数(缩放)', linewidth=2)
        ax4.set_xlabel('相似度阈值')
        ax4.set_ylabel('性能指标')
        ax4.set_title('所有指标综合对比')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_name = os.path.basename(model_path).replace('.ckpt', '')
        plot_path = f"threshold_analysis_{model_name}_{timestamp}.png"
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        
        print(f"📊 阈值分析图表已保存: {plot_path}")
        plt.show()
        
    except Exception as e:
        print(f"⚠️  绘图失败: {e}")

def save_threshold_analysis(results, model_path, dataset_path):
    """保存阈值分析结果"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_name = os.path.basename(model_path).replace('.ckpt', '')
    
    # 保存CSV
    csv_path = f"threshold_analysis_{model_name}_{timestamp}.csv"
    with open(csv_path, 'w', encoding='utf-8') as f:
        f.write("threshold,accuracy,precision,recall,f1_score,tp,tn,fp,fn\n")
        for r in results:
            f.write(f"{r['threshold']},{r.get('accuracy', 0)},{r.get('precision', 0)},{r.get('recall', 0)},{r.get('f1_score', 0)},{r.get('tp', 0)},{r.get('tn', 0)},{r.get('fp', 0)},{r.get('fn', 0)}\n")
    
    # 保存摘要
    summary_path = f"threshold_analysis_summary_{model_name}_{timestamp}.txt"
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write("阈值分析结果摘要\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"模型路径: {model_path}\n")
        f.write(f"数据集路径: {dataset_path}\n")
        f.write("="*50 + "\n\n")
        
        # 最佳结果
        best_accuracy = max(results, key=lambda x: x.get('accuracy', 0))
        best_f1 = max(results, key=lambda x: x.get('f1_score', 0))
        
        f.write("最佳结果:\n")
        f.write(f"最高准确率: 阈值={best_accuracy['threshold']}, 准确率={best_accuracy.get('accuracy', 0):.1f}%\n")
        f.write(f"最高F1分数: 阈值={best_f1['threshold']}, F1={best_f1.get('f1_score', 0):.2f}\n\n")
        
        # 详细结果
        f.write("详细结果:\n")
        f.write("阈值\t准确率\t精确率\t召回率\tF1分数\n")
        for r in results:
            f.write(f"{r['threshold']:.2f}\t{r.get('accuracy', 0):.1f}%\t{r.get('precision', 0):.1f}%\t{r.get('recall', 0):.1f}%\t{r.get('f1_score', 0):.2f}\n")
    
    print(f"💾 阈值分析结果已保存:")
    print(f"   详细数据: {csv_path}")
    print(f"   结果摘要: {summary_path}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="寻找最佳相似度阈值")
    parser.add_argument('--model', type=str, required=True, help='模型路径')
    parser.add_argument('--dataset', type=str, default='data/makeup_dataset', help='数据集路径')
    parser.add_argument('--min_threshold', type=float, default=0.1, help='最小阈值')
    parser.add_argument('--max_threshold', type=float, default=0.9, help='最大阈值')
    parser.add_argument('--step', type=float, default=0.05, help='阈值步长')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        sys.exit(1)
    
    if not os.path.exists(args.dataset):
        print(f"❌ 数据集路径不存在: {args.dataset}")
        sys.exit(1)
    
    try:
        results = find_optimal_threshold(
            args.model, 
            args.dataset, 
            threshold_range=(args.min_threshold, args.max_threshold),
            step=args.step
        )
        
        if results:
            print(f"\n🎉 阈值分析完成!")
        
    except KeyboardInterrupt:
        print(f"\n⚠️  用户中断执行")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
